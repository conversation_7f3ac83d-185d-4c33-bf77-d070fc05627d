<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mediasoup WebRTC Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        video {
            width: 300px;
            height: 200px;
            background-color: #000;
            border-radius: 4px;
            margin: 10px;
        }
        .stats {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Mediasoup WebRTC Test</h1>
        
        <div id="status" class="status info">Ready to test WebRTC connection</div>
        
        <div>
            <button id="startBtn" onclick="startTest()">Start WebRTC Test</button>
            <button id="stopBtn" onclick="stopTest()" disabled>Stop Test</button>
            <button id="statsBtn" onclick="getStats()" disabled>Get Stats</button>
        </div>
        
        <div>
            <h3>Local Video</h3>
            <video id="localVideo" autoplay muted playsinline></video>
        </div>

        <div>
            <h3>Remote Video (Mirrored from Server)</h3>
            <video id="remoteVideo" autoplay playsinline></video>
        </div>
        
        <div>
            <h3>Connection Stats</h3>
            <div id="stats" class="stats">No stats available</div>
        </div>
        
        <div>
            <h3>Debug Log</h3>
            <div id="log" class="stats">Waiting for connection...</div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="/mediasoup-client.js" onload="console.log('✅ mediasoupClient loaded:', typeof mediasoupClient)" onerror="console.error('❌ Failed to load mediasoupClient')"></script>
    <script>
        const socket = io();
        let device;
        let sendTransport;
        let recvTransport;
        let localStream;
        let videoProducer;
        let audioProducer;
        let videoConsumer;
        let audioConsumer;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        async function startTest() {
            try {
                document.getElementById('startBtn').disabled = true;
                setStatus('Starting WebRTC test...', 'info');
                log('🚀 Starting WebRTC test');

                // Check if mediasoupClient is loaded
                if (typeof mediasoupClient === 'undefined') {
                    throw new Error('mediasoupClient library not loaded. Check console for loading errors.');
                }

                // Get user media
                log('📹 Requesting camera and microphone access...');
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 },
                    audio: true
                });

                document.getElementById('localVideo').srcObject = localStream;
                log('✅ Got local media stream');

                // Load mediasoup device
                log('📱 Loading mediasoup device...');
                log('📱 mediasoupClient available: ' + (typeof mediasoupClient !== 'undefined'));
                const { Device } = mediasoupClient;
                device = new Device();
                
                // Get router capabilities
                log('🔗 Getting router RTP capabilities...');
                const routerRtpCapabilities = await new Promise((resolve, reject) => {
                    socket.emit('getRouterRtpCapabilities', (capabilities) => {
                        if (capabilities.error) {
                            reject(new Error(capabilities.error));
                        } else {
                            resolve(capabilities);
                        }
                    });
                });
                
                await device.load({ routerRtpCapabilities });
                log('✅ Device loaded successfully');
                
                // Create send transport
                log('🚛 Creating send transport...');
                const transportInfo = await new Promise((resolve, reject) => {
                    socket.emit('createWebRtcTransport', { direction: 'send' }, (result) => {
                        if (result.error) {
                            reject(new Error(result.error));
                        } else {
                            resolve(result);
                        }
                    });
                });
                
                // Add ICE servers for better connectivity
                transportInfo.iceServers = [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ];

                sendTransport = device.createSendTransport(transportInfo);
                log('✅ Send transport created');
                
                // Handle transport events
                sendTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
                    try {
                        log('🔗 Connecting send transport...');
                        const result = await new Promise((resolve, reject) => {
                            socket.emit('connectWebRtcTransport', {
                                transportId: sendTransport.id,
                                dtlsParameters
                            }, (result) => {
                                if (result.error) {
                                    reject(new Error(result.error));
                                } else {
                                    resolve(result);
                                }
                            });
                        });
                        
                        log('✅ Send transport connected');
                        callback();
                    } catch (error) {
                        log(`❌ Error connecting transport: ${error.message}`);
                        errback(error);
                    }
                });

                // Add ICE connection state monitoring
                sendTransport.on('connectionstatechange', (state) => {
                    log(`🔗 Send transport connection state: ${state}`);
                });

                sendTransport.on('icestatechange', (state) => {
                    log(`🧊 Send transport ICE state: ${state}`);
                });

                sendTransport.on('dtlsstatechange', (state) => {
                    log(`🔒 Send transport DTLS state: ${state}`);
                });

                sendTransport.on('produce', async (parameters, callback, errback) => {
                    try {
                        log(`🎬 Producing ${parameters.kind}...`);
                        const result = await new Promise((resolve, reject) => {
                            socket.emit('produce', {
                                transportId: sendTransport.id,
                                kind: parameters.kind,
                                rtpParameters: parameters.rtpParameters
                            }, (result) => {
                                if (result.error) {
                                    reject(new Error(result.error));
                                } else {
                                    resolve(result);
                                }
                            });
                        });
                        
                        log(`✅ ${parameters.kind} producer created: ${result.id}`);
                        callback({ id: result.id });
                    } catch (error) {
                        log(`❌ Error producing ${parameters.kind}: ${error.message}`);
                        errback(error);
                    }
                });
                
                // Produce video
                const videoTrack = localStream.getVideoTracks()[0];
                if (videoTrack) {
                    // Debug video track before producing
                    log(`🔍 Video track state: ${videoTrack.readyState}`);
                    log(`🔍 Video track enabled: ${videoTrack.enabled}`);
                    log(`🔍 Video track muted: ${videoTrack.muted}`);
                    log(`🔍 Video track settings: ${JSON.stringify(videoTrack.getSettings())}`);

                    videoProducer = await sendTransport.produce({ track: videoTrack });
                    log(`✅ Video producer created: ${videoProducer.id}`);

                    // Monitor producer stats
                    setInterval(async () => {
                        try {
                            const stats = await videoProducer.getStats();
                            log(`📊 Video producer stats: ${stats.length} entries`);
                            if (stats.length > 0) {
                                const outbound = stats.find(s => s.type === 'outbound-rtp');
                                if (outbound) {
                                    log(`📊 Video outbound: ${outbound.packetsSent} packets, ${outbound.bytesSent} bytes`);
                                }
                            }
                        } catch (err) {
                            log(`❌ Error getting video producer stats: ${err.message}`);
                        }
                    }, 3000);
                }

                // Produce audio
                const audioTrack = localStream.getAudioTracks()[0];
                if (audioTrack) {
                    // Debug audio track before producing
                    log(`🔍 Audio track state: ${audioTrack.readyState}`);
                    log(`🔍 Audio track enabled: ${audioTrack.enabled}`);
                    log(`🔍 Audio track muted: ${audioTrack.muted}`);

                    audioProducer = await sendTransport.produce({ track: audioTrack });
                    log(`✅ Audio producer created: ${audioProducer.id}`);
                }
                
                setStatus('WebRTC connection established successfully!', 'success');
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('statsBtn').disabled = false;

                // Create receive transport for consuming our own stream
                await createReceiveTransport();

                // Start periodic stats
                startStatsMonitoring();
                
            } catch (error) {
                log(`❌ Error: ${error.message}`);
                setStatus(`Error: ${error.message}`, 'error');
                document.getElementById('startBtn').disabled = false;
            }
        }

        async function createReceiveTransport() {
            try {
                log('🚛 Creating receive transport...');
                const transportInfo = await new Promise((resolve, reject) => {
                    socket.emit('createWebRtcTransport', { direction: 'recv' }, (result) => {
                        if (result.error) {
                            reject(new Error(result.error));
                        } else {
                            resolve(result);
                        }
                    });
                });

                // Add ICE servers for better connectivity
                transportInfo.iceServers = [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ];

                recvTransport = device.createRecvTransport(transportInfo);
                log('✅ Receive transport created');

                // Handle transport events
                recvTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
                    try {
                        log('🔗 Connecting receive transport...');
                        const result = await new Promise((resolve, reject) => {
                            socket.emit('connectWebRtcTransport', {
                                transportId: recvTransport.id,
                                dtlsParameters
                            }, (result) => {
                                if (result.error) {
                                    reject(new Error(result.error));
                                } else {
                                    resolve(result);
                                }
                            });
                        });

                        log('✅ Receive transport connected');
                        callback();
                    } catch (error) {
                        log(`❌ Error connecting receive transport: ${error.message}`);
                        errback(error);
                    }
                });

                // Add ICE connection state monitoring for receive transport
                recvTransport.on('connectionstatechange', (state) => {
                    log(`🔗 Recv transport connection state: ${state}`);
                });

                recvTransport.on('icestatechange', (state) => {
                    log(`🧊 Recv transport ICE state: ${state}`);
                });

                recvTransport.on('dtlsstatechange', (state) => {
                    log(`🔒 Recv transport DTLS state: ${state}`);
                });

                // Now consume our own producers
                if (videoProducer) {
                    await consumeProducer(videoProducer.id, 'video');
                }
                if (audioProducer) {
                    await consumeProducer(audioProducer.id, 'audio');
                }

            } catch (error) {
                log(`❌ Error creating receive transport: ${error.message}`);
            }
        }

        async function consumeProducer(producerId, kind) {
            try {
                log(`🍽️ Consuming ${kind} producer: ${producerId}`);

                const consumerInfo = await new Promise((resolve, reject) => {
                    socket.emit('consume', {
                        transportId: recvTransport.id,
                        producerId: producerId,
                        rtpCapabilities: device.rtpCapabilities
                    }, (result) => {
                        if (result.error) {
                            reject(new Error(result.error));
                        } else {
                            resolve(result);
                        }
                    });
                });

                const consumer = await recvTransport.consume({
                    id: consumerInfo.id,
                    producerId: consumerInfo.producerId,
                    kind: consumerInfo.kind,
                    rtpParameters: consumerInfo.rtpParameters
                });

                log(`✅ ${kind} consumer created: ${consumer.id}`);
                log(`🔍 Consumer paused: ${consumer.paused}`);
                log(`🔍 Consumer closed: ${consumer.closed}`);
                log(`🔍 Consumer kind: ${consumer.kind}`);
                log(`🔍 Consumer producerId: ${consumer.producerId}`);

                // Monitor consumer stats
                setInterval(async () => {
                    try {
                        const stats = await consumer.getStats();
                        log(`📊 ${kind} consumer stats: ${stats.length} entries`);
                        if (stats.length > 0) {
                            const inbound = stats.find(s => s.type === 'inbound-rtp');
                            if (inbound) {
                                log(`📊 ${kind} inbound: ${inbound.packetsReceived} packets, ${inbound.bytesReceived} bytes`);
                            }
                        }
                    } catch (err) {
                        log(`❌ Error getting ${kind} consumer stats: ${err.message}`);
                    }
                }, 3000);

                // Resume the consumer
                await new Promise((resolve, reject) => {
                    socket.emit('resumeConsumer', {
                        consumerId: consumer.id
                    }, (result) => {
                        if (result.error) {
                            reject(new Error(result.error));
                        } else {
                            resolve(result);
                        }
                    });
                });

                log(`▶️ ${kind} consumer resumed`);

                // Add track to remote video
                if (kind === 'video') {
                    videoConsumer = consumer;
                    const remoteVideo = document.getElementById('remoteVideo');
                    const stream = new MediaStream([consumer.track]);

                    // Debug track state
                    log(`🔍 Consumer track state: ${consumer.track.readyState}`);
                    log(`🔍 Consumer track enabled: ${consumer.track.enabled}`);
                    log(`🔍 Consumer track muted: ${consumer.track.muted}`);
                    log(`🔍 Stream tracks: ${stream.getTracks().length}`);

                    // Add event listeners
                    consumer.track.addEventListener('ended', () => log('❌ Video track ended'));
                    consumer.track.addEventListener('mute', () => log('🔇 Video track muted'));
                    consumer.track.addEventListener('unmute', () => log('🔊 Video track unmuted'));

                    remoteVideo.srcObject = stream;

                    // Add video element event listeners
                    remoteVideo.addEventListener('loadedmetadata', () => log('📺 Remote video metadata loaded'));
                    remoteVideo.addEventListener('canplay', () => log('📺 Remote video can play'));
                    remoteVideo.addEventListener('playing', () => log('📺 Remote video playing'));
                    remoteVideo.addEventListener('error', (e) => log(`❌ Remote video error: ${e.error}`));

                    log('📺 Remote video stream set');
                } else if (kind === 'audio') {
                    audioConsumer = consumer;
                    log('🔊 Audio consumer ready');
                }

            } catch (error) {
                log(`❌ Error consuming ${kind}: ${error.message}`);
            }
        }

        function startStatsMonitoring() {
            setInterval(async () => {
                if (sendTransport) {
                    try {
                        const stats = await sendTransport.getStats();
                        updateStatsDisplay(stats);
                    } catch (error) {
                        log(`❌ Error getting stats: ${error.message}`);
                    }
                }
            }, 2000);
        }
        
        function updateStatsDisplay(stats) {
            const statsDiv = document.getElementById('stats');
            let statsText = 'Transport Stats:\n';
            
            stats.forEach((stat) => {
                if (stat.type === 'transport') {
                    statsText += `ICE State: ${stat.iceState}\n`;
                    statsText += `DTLS State: ${stat.dtlsState}\n`;
                    statsText += `Bytes Sent: ${stat.bytesSent}\n`;
                    statsText += `Bytes Received: ${stat.bytesReceived}\n`;
                    statsText += `RTP Bytes Sent: ${stat.rtpBytesSent}\n`;
                    statsText += `Send Bitrate: ${stat.sendBitrate} bps\n`;
                }
            });
            
            statsDiv.textContent = statsText;
        }
        
        async function getStats() {
            try {
                log('📊 Getting transport stats...');
                const result = await new Promise((resolve, reject) => {
                    socket.emit('getTransportStats', {
                        transportId: sendTransport.id
                    }, (result) => {
                        if (result.error) {
                            reject(new Error(result.error));
                        } else {
                            resolve(result);
                        }
                    });
                });
                
                log('📊 Server stats: ' + JSON.stringify(result.stats, null, 2));
            } catch (error) {
                log(`❌ Error getting stats: ${error.message}`);
            }
        }
        
        function stopTest() {
            try {
                log('🛑 Stopping test...');
                
                if (localStream) {
                    localStream.getTracks().forEach(track => track.stop());
                    localStream = null;
                }
                
                if (videoProducer) {
                    videoProducer.close();
                    videoProducer = null;
                }
                
                if (audioProducer) {
                    audioProducer.close();
                    audioProducer = null;
                }
                
                if (videoConsumer) {
                    videoConsumer.close();
                    videoConsumer = null;
                }

                if (audioConsumer) {
                    audioConsumer.close();
                    audioConsumer = null;
                }

                if (recvTransport) {
                    recvTransport.close();
                    recvTransport = null;
                }

                if (sendTransport) {
                    sendTransport.close();
                    sendTransport = null;
                }

                document.getElementById('localVideo').srcObject = null;
                document.getElementById('remoteVideo').srcObject = null;
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                document.getElementById('statsBtn').disabled = true;
                
                setStatus('Test stopped', 'info');
                log('✅ Test stopped successfully');
                
            } catch (error) {
                log(`❌ Error stopping test: ${error.message}`);
            }
        }
        
        // Socket events
        socket.on('connect', () => {
            log('🔌 Connected to server');
            log('📚 Checking mediasoupClient: ' + (typeof mediasoupClient !== 'undefined' ? 'Available' : 'Not available'));
        });

        socket.on('disconnect', () => {
            log('🔌 Disconnected from server');
            setStatus('Disconnected from server', 'error');
        });

        // Check if mediasoupClient loads after page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('📚 Page loaded. mediasoupClient status: ' + (typeof mediasoupClient !== 'undefined' ? 'Available' : 'Not available'));
                if (typeof mediasoupClient !== 'undefined') {
                    log('📚 mediasoupClient version: ' + (mediasoupClient.version || 'unknown'));
                }
            }, 1000);
        });
    </script>
</body>
</html>
