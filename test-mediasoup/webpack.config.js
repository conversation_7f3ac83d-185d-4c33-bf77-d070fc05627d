const path = require('path');

module.exports = {
  entry: './src/mediasoup-client-bundle.js',
  output: {
    filename: 'mediasoup-client.js',
    path: path.resolve(__dirname, 'public'),
  },
  mode: 'development',
  resolve: {
    fallback: {
      "events": require.resolve("events/"),
      "util": require.resolve("util/"),
      "buffer": require.resolve("buffer/"),
      "process": require.resolve("process/browser")
    }
  },
  plugins: [
    new (require('webpack')).ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer'],
    }),
  ]
};
