#!/bin/bash

# Mediasoup Test Server Deployment Script
set -e

SERVER_IP="**************"
SSH_KEY="~/.ssh/oci_key"
REMOTE_DIR="/opt/mediasoup-test"

echo "🚀 Deploying Mediasoup test server to $SERVER_IP"

# Create remote directory
ssh -i $SSH_KEY -o StrictHostKeyChecking=no ubuntu@$SERVER_IP "sudo mkdir -p $REMOTE_DIR && sudo chown ubuntu:ubuntu $REMOTE_DIR"

# Copy files
echo "📁 Copying files..."
scp -i $SSH_KEY -o StrictHostKeyChecking=no -r ./* ubuntu@$SERVER_IP:$REMOTE_DIR/

# Install dependencies and start
ssh -i $SSH_KEY -o StrictHostKeyChecking=no ubuntu@$SERVER_IP << 'EOF'
cd /opt/mediasoup-test

echo "📦 Installing dependencies..."
npm install

echo "🔧 Creating systemd service..."
sudo tee /etc/systemd/system/mediasoup-test.service > /dev/null << 'SERVICE'
[Unit]
Description=Mediasoup Test Server
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/opt/mediasoup-test
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=ANNOUNCED_IP=**************

[Install]
WantedBy=multi-user.target
SERVICE

echo "🔄 Starting service..."
sudo systemctl daemon-reload
sudo systemctl enable mediasoup-test
sudo systemctl restart mediasoup-test

echo "📊 Service status:"
sudo systemctl status mediasoup-test --no-pager

echo "🌐 Test server should be available at: http://**************:3000"
EOF

echo "✅ Deployment complete!"
echo "🌐 Open http://**************:3000 in your browser to test"
