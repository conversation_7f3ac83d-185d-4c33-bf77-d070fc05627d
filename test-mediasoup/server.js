const express = require('express');
const http = require('http');
const https = require('https');
const fs = require('fs');
const socketIo = require('socket.io');
const mediasoup = require('mediasoup');
const path = require('path');

const app = express();

// Try to use HTTPS if certificates exist, fallback to HTTP
let server;
try {
  const options = {
    key: fs.readFileSync(path.join(__dirname, 'key.pem')),
    cert: fs.readFileSync(path.join(__dirname, 'cert.pem'))
  };
  server = https.createServer(options, app);
  console.log('🔒 Using HTTPS server');
} catch (error) {
  server = http.createServer(app);
  console.log('🔓 Using HTTP server (certificates not found)');
}
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Mediasoup configuration
const config = {
  worker: {
    rtcMinPort: 40000,
    rtcMaxPort: 40100,
    logLevel: 'warn',
    logTags: ['info', 'ice', 'dtls', 'rtp', 'srtp', 'rtcp']
  },
  router: {
    mediaCodecs: [
      {
        kind: 'audio',
        mimeType: 'audio/opus',
        clockRate: 48000,
        channels: 2
      },
      {
        kind: 'video',
        mimeType: 'video/VP8',
        clockRate: 90000,
        parameters: {
          'x-google-start-bitrate': 1000
        }
      }
    ]
  },
  webRtcTransport: {
    listenIps: [
      {
        ip: '0.0.0.0',
        announcedIp: process.env.ANNOUNCED_IP || null
      }
    ],
    enableUdp: true,
    enableTcp: true,
    preferUdp: true,
    initialAvailableOutgoingBitrate: 1000000
  }
};

let worker;
let router;
const transports = new Map();
const producers = new Map();
const consumers = new Map();

async function createWorker() {
  worker = await mediasoup.createWorker({
    rtcMinPort: config.worker.rtcMinPort,
    rtcMaxPort: config.worker.rtcMaxPort,
    logLevel: config.worker.logLevel,
    logTags: config.worker.logTags
  });

  console.log('✅ Mediasoup worker created [pid:%d]', worker.pid);

  worker.on('died', (error) => {
    console.error('❌ Mediasoup worker died:', error);
    setTimeout(() => process.exit(1), 2000);
  });

  router = await worker.createRouter({ mediaCodecs: config.router.mediaCodecs });
  console.log('✅ Mediasoup router created');
}

io.on('connection', (socket) => {
  console.log('🔌 Client connected:', socket.id);

  socket.on('getRouterRtpCapabilities', (callback) => {
    console.log('📊 Client requesting router RTP capabilities');
    callback(router.rtpCapabilities);
  });

  socket.on('createWebRtcTransport', async (data, callback) => {
    try {
      console.log('🚀 Creating WebRTC transport for:', data.direction);
      
      const transport = await router.createWebRtcTransport(config.webRtcTransport);
      
      console.log('📊 WebRTC transport created:', {
        id: transport.id,
        iceParameters: transport.iceParameters,
        iceCandidates: transport.iceCandidates,
        dtlsParameters: transport.dtlsParameters
      });

      transports.set(transport.id, transport);

      callback({
        id: transport.id,
        iceParameters: transport.iceParameters,
        iceCandidates: transport.iceCandidates,
        dtlsParameters: transport.dtlsParameters
      });
    } catch (error) {
      console.error('❌ Error creating WebRTC transport:', error);
      callback({ error: error.message });
    }
  });

  socket.on('connectWebRtcTransport', async (data, callback) => {
    try {
      console.log('🔗 Connecting WebRTC transport:', data.transportId);
      
      const transport = transports.get(data.transportId);
      if (!transport) {
        throw new Error('Transport not found');
      }

      await transport.connect({
        dtlsParameters: data.dtlsParameters
      });

      console.log('✅ WebRTC transport connected');

      // Monitor transport state
      console.log(`📊 Transport state: ${transport.connectionState}, ICE: ${transport.iceState}, DTLS: ${transport.dtlsState}`);

      // Add transport event listeners
      transport.on('icestatechange', (iceState) => {
        console.log(`🧊 Transport ICE state changed: ${iceState}`);
      });

      transport.on('dtlsstatechange', (dtlsState) => {
        console.log(`🔐 Transport DTLS state changed: ${dtlsState}`);
      });
      callback({ success: true });
    } catch (error) {
      console.error('❌ Error connecting WebRTC transport:', error);
      callback({ error: error.message });
    }
  });

  socket.on('produce', async (data, callback) => {
    try {
      console.log('🎬 Creating producer for:', data.kind);

      const transport = transports.get(data.transportId);
      if (!transport) {
        throw new Error('Transport not found');
      }

      const producer = await transport.produce({
        kind: data.kind,
        rtpParameters: data.rtpParameters
      });

      console.log('✅ Producer created:', {
        id: producer.id,
        kind: producer.kind,
        paused: producer.paused
      });

      producers.set(producer.id, producer);

      // Log producer stats periodically with more detail
      setInterval(async () => {
        try {
          const stats = await producer.getStats();
          console.log(`📊 Producer ${producer.kind} stats: ${stats.length} entries`);

          if (stats.length > 0) {
            stats.forEach(stat => {
              if (stat.type === 'inbound-rtp') {
                console.log(`📊 ${producer.kind} inbound: ${stat.packetsReceived} packets, ${stat.bytesReceived} bytes, ${stat.packetsLost} lost`);
              }
            });
          } else {
            console.log(`⚠️ No stats for ${producer.kind} producer - no media flowing`);
          }

          // Check producer state
          console.log(`📊 Producer ${producer.kind} state: paused=${producer.paused}, closed=${producer.closed}`);
        } catch (err) {
          console.error('Error getting producer stats:', err);
        }
      }, 5000);

      // Notify all other clients about the new producer
      socket.broadcast.emit('newProducer', {
        producerId: producer.id,
        kind: producer.kind
      });

      callback({ id: producer.id });
    } catch (error) {
      console.error('❌ Error creating producer:', error);
      callback({ error: error.message });
    }
  });

  socket.on('consume', async (data, callback) => {
    try {
      console.log('🍽️ Creating consumer for producer:', data.producerId);

      const transport = transports.get(data.transportId);
      const producer = producers.get(data.producerId);

      if (!transport) {
        throw new Error('Transport not found');
      }
      if (!producer) {
        throw new Error('Producer not found');
      }

      const consumer = await transport.consume({
        producerId: data.producerId,
        rtpCapabilities: data.rtpCapabilities,
        paused: true
      });

      console.log('✅ Consumer created:', {
        id: consumer.id,
        kind: consumer.kind,
        producerId: consumer.producerId,
        paused: consumer.paused,
        closed: consumer.closed
      });

      consumers.set(consumer.id, consumer);

      // Monitor consumer stats
      setInterval(async () => {
        try {
          const stats = await consumer.getStats();
          console.log(`📊 Consumer ${consumer.kind} stats: ${stats.length} entries`);

          if (stats.length > 0) {
            stats.forEach(stat => {
              if (stat.type === 'outbound-rtp') {
                console.log(`📊 ${consumer.kind} outbound: ${stat.packetsSent} packets, ${stat.bytesSent} bytes`);
              }
            });
          } else {
            console.log(`⚠️ No stats for ${consumer.kind} consumer - no media flowing`);
          }

          console.log(`📊 Consumer ${consumer.kind} state: paused=${consumer.paused}, closed=${consumer.closed}`);
        } catch (err) {
          console.error('Error getting consumer stats:', err);
        }
      }, 5000);

      callback({
        id: consumer.id,
        kind: consumer.kind,
        rtpParameters: consumer.rtpParameters,
        producerId: consumer.producerId
      });
    } catch (error) {
      console.error('❌ Error creating consumer:', error);
      callback({ error: error.message });
    }
  });

  socket.on('resumeConsumer', async (data, callback) => {
    try {
      console.log('▶️ Resuming consumer:', data.consumerId);

      const consumer = consumers.get(data.consumerId);
      if (!consumer) {
        throw new Error('Consumer not found');
      }

      await consumer.resume();
      console.log('✅ Consumer resumed');

      callback({ success: true });
    } catch (error) {
      console.error('❌ Error resuming consumer:', error);
      callback({ error: error.message });
    }
  });

  socket.on('getTransportStats', async (data, callback) => {
    try {
      const transport = transports.get(data.transportId);
      if (!transport) {
        throw new Error('Transport not found');
      }

      const stats = await transport.getStats();
      console.log('📊 Transport stats:', stats);
      callback({ stats });
    } catch (error) {
      console.error('❌ Error getting transport stats:', error);
      callback({ error: error.message });
    }
  });

  socket.on('disconnect', () => {
    console.log('🔌 Client disconnected:', socket.id);
  });
});

async function main() {
  try {
    await createWorker();
    
    const PORT = process.env.PORT || 3000;
    server.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 Mediasoup test server running on port ${PORT}`);
      console.log(`📊 WebRTC port range: ${config.worker.rtcMinPort}-${config.worker.rtcMaxPort}`);
      console.log(`🌐 Announced IP: ${process.env.ANNOUNCED_IP || 'auto-detect'}`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

main();
