# OCI Provider Configuration
variable "tenancy_ocid" {
  description = "OCI Tenancy OCID"
  type        = string
}

variable "user_ocid" {
  description = "OCI User OCID"
  type        = string
}

variable "fingerprint" {
  description = "OCI API Key Fingerprint"
  type        = string
}

variable "private_key_path" {
  description = "Path to OCI API private key file"
  type        = string
}

variable "region" {
  description = "OCI Region"
  type        = string
  default     = "us-phoenix-1"
}

variable "compartment_id" {
  description = "OCI Compartment OCID"
  type        = string
}

# Application Configuration
variable "app_name" {
  description = "Application name"
  type        = string
  default     = "sai-platform"
}

# Instance Configuration
variable "instance_ocpus" {
  description = "Number of OCPUs for the instance"
  type        = number
  default     = 1
}

variable "instance_memory_gb" {
  description = "Memory in GB for the instance"
  type        = number
  default     = 6
}

variable "instance_shape" {
  description = "Instance shape to use"
  type        = string
  default     = "VM.Standard.A1.Flex"
}

variable "ssh_public_key" {
  description = "SSH public key for instance access"
  type        = string
}

# Package Configuration
variable "package_name" {
  description = "Name of the media server package"
  type        = string
  default     = "media-server"
}

variable "package_version" {
  description = "Version of the media server package"
  type        = string
  default     = "1.0.0"
}

# Networking (Optional)
variable "vcn_id" {
  description = "Existing VCN OCID (optional)"
  type        = string
  default     = null
}

variable "subnet_id" {
  description = "Existing subnet OCID (optional)"
  type        = string
  default     = null
}
