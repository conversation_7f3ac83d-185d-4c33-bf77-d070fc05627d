output "media_server_public_ip" {
  description = "Public IP address of the media server"
  value       = module.oci_media_server.media_server_public_ip
}

output "media_server_private_ip" {
  description = "Private IP address of the media server"
  value       = module.oci_media_server.media_server_private_ip
}

output "media_server_url" {
  description = "URL of the media server"
  value       = module.oci_media_server.media_server_url
}

output "instance_id" {
  description = "OCID of the media server instance"
  value       = module.oci_media_server.instance_id
}

output "ssh_command" {
  description = "SSH command to connect to the media server"
  value       = "ssh opc@${module.oci_media_server.media_server_public_ip}"
}

output "deployment_info" {
  description = "Deployment information"
  value = {
    instance_shape = var.instance_shape
    ocpus         = var.instance_ocpus
    memory_gb     = var.instance_memory_gb
    region        = var.region
    architecture  = contains(["VM.Standard.A1.Flex", "VM.Standard.A2.Flex"], var.instance_shape) ? "ARM64" : "x86_64"
    note          = "Deployed with fallback packages (no artifact registry)"
  }
}
