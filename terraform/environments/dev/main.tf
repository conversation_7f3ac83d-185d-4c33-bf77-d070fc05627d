# Development Environment Configuration

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.23"
    }
  }

  # Optional: Configure remote state backend for development
  # backend "gcs" {
  #   bucket = "your-terraform-state-bucket"
  #   prefix = "webrtc-platform/dev"
  # }
}

provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Kubernetes provider configuration (disabled for now)
# We'll deploy Kubernetes resources manually after cluster creation
# provider "kubernetes" {
#   host                   = "https://${module.webrtc_platform.media_cluster_endpoint}"
#   token                  = data.google_client_config.default.access_token
#   cluster_ca_certificate = base64decode(module.webrtc_platform.media_cluster_ca_certificate)
# }

# Local values for development environment
locals {
  environment = "dev"
  name_prefix = "${var.app_name}-${local.environment}"
  common_labels = {
    project     = var.app_name
    environment = local.environment
    managed-by  = "terraform"
    region      = var.region
  }
}

# Call the main module with development-specific settings
module "sai_platform" {
  source = "../.."

  # Project Configuration
  project_id  = var.project_id
  region      = var.region
  zone        = var.zone
  environment = local.environment

  # Application Configuration
  app_name    = var.app_name
  domain_name = var.domain_name

  # Networking Configuration
  vpc_name    = "${local.name_prefix}-vpc"
  subnet_cidr = "10.0.0.0/24"

  # Docker Images (use Artifact Registry)
  signaling_server_image = "us-central1-docker.pkg.dev/${var.project_id}/sai-platform-${local.environment}-repo/sai-signaling:latest"

  # Cloud Run Configuration (minimum for gen2)
  cloud_run_cpu          = "1"
  cloud_run_memory       = "1Gi"
  cloud_run_max_instances = 3

  # TURN Server Configuration (smaller instance for dev)
  turn_server_machine_type = "e2-micro"
  turn_server_image       = var.turn_server_image
  turn_username           = var.turn_username
  turn_password           = var.turn_password

  # Monitoring Configuration
  enable_monitoring  = false  # Disabled for dev to save costs
  notification_email = var.notification_email

  # Security Configuration (more permissive for dev)
  allowed_source_ranges = ["0.0.0.0/0"]

  # Feature Flags
  enable_ssl    = false
  enable_cdn    = false
  enable_backup = false

  # Resource Labels
  labels = local.common_labels
}
