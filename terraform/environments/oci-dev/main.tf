# OCI Development Environment for Media Server
# This configuration deploys the media server to Oracle Cloud Infrastructure

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    oci = {
      source  = "oracle/oci"
      version = "~> 5.0"
    }
  }
}

# Configure OCI Provider
provider "oci" {
  tenancy_ocid     = var.tenancy_ocid
  user_ocid        = var.user_ocid
  fingerprint      = var.fingerprint
  private_key_path = var.private_key_path
  region           = var.region
}

# Local values
locals {
  environment = "oci-dev"
  name_prefix = "${var.app_name}-${local.environment}"
  
  common_tags = {
    Environment = local.environment
    Application = var.app_name
    ManagedBy   = "terraform"
    Project     = "sai-platform"
  }
}

# Create OCI Artifact Registry for Media Server Packages
module "artifact_registry" {
  source = "../../modules/oci-artifact-registry"

  # OCI Configuration
  compartment_id = var.compartment_id
  user_ocid      = var.user_ocid
  region         = var.region
  name_prefix    = local.name_prefix
  environment    = var.environment

  # Tags
  freeform_tags = local.common_tags
}

# Deploy Media Server to OCI
module "oci_media_server" {
  source = "../../modules/oci-media-server"

  # OCI Configuration
  compartment_id = var.compartment_id
  tenancy_ocid   = var.tenancy_ocid
  name_prefix    = local.name_prefix

  # Instance Configuration
  instance_shape     = var.instance_shape
  instance_ocpus     = var.instance_ocpus
  instance_memory_gb = var.instance_memory_gb
  ssh_public_key     = var.ssh_public_key

  # Application Configuration
  package_name    = var.package_name
  package_version = var.package_version
  jwt_secret      = var.jwt_secret
  artifact_url    = module.artifact_registry.repository_endpoint_template
  auth_token      = module.artifact_registry.auth_token

  # Networking (optional - will create new VCN/subnet if not provided)
  vcn_id    = var.vcn_id
  subnet_id = var.subnet_id

  # Tags
  freeform_tags = local.common_tags

  depends_on = [module.artifact_registry]
}
