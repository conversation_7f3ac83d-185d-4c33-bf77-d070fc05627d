# OCI Development Environment Variables
# Copy this file to terraform.tfvars and update with your values

# OCI Provider Configuration
tenancy_ocid     = "ocid1.tenancy.oc1..aaaaaaaa..."
user_ocid        = "ocid1.user.oc1..aaaaaaaa..."
fingerprint      = "aa:bb:cc:dd:ee:ff:gg:hh:ii:jj:kk:ll:mm:nn:oo:pp"
private_key_path = "~/.oci/oci_api_key.pem"
# Changed from us-ashburn-1 to us-phoenix-1 for better Ampere A1 availability
# Trial accounts often have better capacity in Phoenix region
region           = "us-phoenix-1"
compartment_id   = "ocid1.compartment.oc1..aaaaaaaa..."

# Application Configuration
app_name = "sai-platform"

# Instance Configuration (Ampere A1 - ARM64)
instance_ocpus     = 1    # 1-4 OCPUs available
instance_memory_gb = 6    # 1-24 GB available (6GB per OCPU recommended)

# Instance Shape Options (uncomment to use fallback if A1 unavailable)
# instance_shape = "VM.Standard.A1.Flex"      # Default: Ampere A1 (ARM64) - Always Free
# instance_shape = "VM.Standard.A2.Flex"      # Fallback: Ampere A2 (ARM64) - Paid
# instance_shape = "VM.Standard.E2.1.Micro"   # Fallback: x86 Micro - Always Free

# SSH Configuration
ssh_public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... your-public-key"

# Package Configuration
package_name    = "media-server"
package_version = "1.0.0"  # Specific version for tgz file

# Networking (Optional - leave empty to create new VCN/subnet)
vcn_id    = null
subnet_id = null
