# OCI Provider Configuration
variable "tenancy_ocid" {
  description = "OCI Tenancy OCID"
  type        = string
}

variable "user_ocid" {
  description = "OCI User OCID"
  type        = string
}

variable "fingerprint" {
  description = "OCI API Key Fingerprint"
  type        = string
}

variable "private_key_path" {
  description = "Path to OCI API private key file"
  type        = string
}

variable "region" {
  description = "OCI Region"
  type        = string
  default     = "us-ashburn-1"
}

variable "compartment_id" {
  description = "OCI Compartment OCID"
  type        = string
}

# Application Configuration
variable "app_name" {
  description = "Application name"
  type        = string
  default     = "sai-platform"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

# Instance Configuration
variable "instance_ocpus" {
  description = "Number of OCPUs for the Ampere A1 instance"
  type        = number
  default     = 1
}

variable "instance_memory_gb" {
  description = "Memory in GB for the instance"
  type        = number
  default     = 6
}

variable "instance_shape" {
  description = "Instance shape to use"
  type        = string
  default     = "VM.Standard.A1.Flex"
  validation {
    condition = contains([
      "VM.Standard.A1.Flex",      # Ampere A1 (ARM64) - Always Free
      "VM.Standard.A2.Flex",      # Ampere A2 (ARM64) - Paid
      "VM.Standard.E2.1.Micro"    # x86 Micro - Always Free
    ], var.instance_shape)
    error_message = "Instance shape must be one of: VM.Standard.A1.Flex, VM.Standard.A2.Flex, or VM.Standard.E2.1.Micro"
  }
}

variable "ssh_public_key" {
  description = "SSH public key for instance access"
  type        = string
}

# Package Configuration
variable "package_name" {
  description = "Name of the media server package"
  type        = string
  default     = "media-server"
}

variable "package_version" {
  description = "Version of the media server package"
  type        = string
  default     = "1.0.0"
}

variable "jwt_secret" {
  description = "JWT secret key for media server authentication"
  type        = string
  sensitive   = true
  default     = "your-secret-key-change-in-production"
}

# Networking (Optional)
variable "vcn_id" {
  description = "Existing VCN OCID (optional - will create new VCN if not provided)"
  type        = string
  default     = null
}

variable "subnet_id" {
  description = "Existing subnet OCID (optional - will create new subnet if not provided)"
  type        = string
  default     = null
}
