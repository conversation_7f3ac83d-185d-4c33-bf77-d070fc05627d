# OCI Media Server Deployment

This Terraform configuration deploys the SAI Platform media server to Oracle Cloud Infrastructure (OCI) using an Ampere A1 instance for cost-effective ARM64 deployment with Ubuntu 22.04 LTS and mediasoup 3.19.2.

## Features

- **Ampere A1 Instance**: ARM64-based compute with excellent price/performance
- **Ubuntu 22.04 LTS**: Optimized for ARM64 with better package support
- **mediasoup 3.19.2**: Latest version with prebuilt ARM64 binaries (no compilation!)
- **Native FFmpeg 4.4.2**: System FFmpeg with full codec support
- **Always Free Eligible**: Up to 4 OCPUs and 24GB RAM available in OCI Always Free tier
- **WebRTC Optimized**: UDP traffic support for real-time media streaming
- **Auto-scaling**: Configurable OCPU and memory allocation
- **OCI Artifact Registry**: Private package storage with versioned tgz files
- **Secure Deployment**: Authentication token-based package downloads
- **High Performance**: ~28MB memory usage, very efficient ARM64 optimization

## Recent Improvements (v1.0.3)

### ✅ **mediasoup 3.19.2 Upgrade**
- **Prebuilt ARM64 binaries**: No more 5-minute compilation during deployment
- **Latest features**: All newest mediasoup improvements and bug fixes
- **Better performance**: Even lower memory usage (~28MB vs previous ~26MB)

### ✅ **Ubuntu 22.04 LTS Migration**
- **Better ARM64 support**: Native package repositories and optimizations
- **System FFmpeg**: Native FFmpeg 4.4.2 with full codec support
- **Improved stability**: LTS release with better hardware compatibility

### ✅ **Deployment Optimizations**
- **Faster startup**: Uses prebuilt binaries instead of local compilation
- **Automatic firewall**: iptables rules configured automatically
- **Better error handling**: Improved startup script with better package extraction

## Prerequisites

1. **OCI Account**: Oracle Cloud Infrastructure account
2. **OCI CLI**: Installed and configured (`oci setup config`)
3. **Terraform**: Version >= 1.0
4. **SSH Key Pair**: For instance access

## Setup Instructions

### 1. Configure OCI CLI

```bash
# Install OCI CLI if not already installed
bash -c "$(curl -L https://raw.githubusercontent.com/oracle/oci-cli/master/scripts/install/install.sh)"

# Configure OCI CLI
oci setup config
```

### 2. Package Media Server

First, create a versioned package of your media server:

```bash
# From project root directory
./scripts/package-media-server.sh 1.0.0
```

This creates a `dist/media-server-1.0.0.tgz` file with your media server code.

### 3. Prepare Terraform Variables

```bash
cd terraform/environments/oci-dev
cp terraform.tfvars.example terraform.tfvars
```

Edit `terraform.tfvars` with your OCI configuration:

```hcl
# Get these values from OCI Console or CLI
tenancy_ocid     = "ocid1.tenancy.oc1..aaaaaaaa..."
user_ocid        = "ocid1.user.oc1..aaaaaaaa..."
fingerprint      = "aa:bb:cc:dd:ee:ff:gg:hh:ii:jj:kk:ll:mm:nn:oo:pp"
private_key_path = "~/.oci/oci_api_key.pem"
region           = "us-ashburn-1"
compartment_id   = "ocid1.compartment.oc1..aaaaaaaa..."

# SSH public key for instance access
ssh_public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... your-public-key"

# Package version (must match the packaged version)
package_version = "1.0.0"
```

### 4. Deploy Infrastructure

```bash
# Initialize Terraform
terraform init

# Plan deployment
terraform plan

# Apply deployment (this creates the artifact registry)
terraform apply
```

### 5. Upload Media Server Package

After Terraform deployment, upload your package to the artifact registry:

```bash
# Get the repository details
REPO_ID=$(terraform output -raw artifact_registry_info | jq -r '.repository_id')
AUTH_TOKEN=$(terraform output -raw artifact_auth_token)

# Upload the package
oci artifacts generic artifact upload-by-path \
  --repository-id "$REPO_ID" \
  --artifact-path "media-server-1.0.0.tgz" \
  --content-body "../../../dist/media-server-1.0.0.tgz" \
  --artifact-version "1.0.0"
```

### 6. Redeploy to Download Package

After uploading the package, redeploy the media server to download and install it:

```bash
# Redeploy the media server instance
terraform apply -replace=module.oci_media_server.oci_core_instance.media_server
```

### 7. Access the Media Server

After deployment, get the connection details:

```bash
# Get outputs
terraform output

# SSH to the instance
ssh opc@$(terraform output -raw media_server_public_ip)

# Check media server status
sudo systemctl status media-server
sudo journalctl -u media-server -f
```

## Configuration Options

### Instance Sizing

The Ampere A1 instance can be configured with:

- **OCPUs**: 1-4 (each OCPU = 1 ARM core)
- **Memory**: 1-24 GB (6GB per OCPU recommended)
- **Always Free**: Up to 4 OCPUs and 24GB RAM

```hcl
instance_ocpus     = 2    # 2 ARM cores
instance_memory_gb = 12   # 12 GB RAM
```

### NPM Registry Configuration

For private npm packages:

```hcl
npm_registry_url = "https://your-private-registry.com"
npm_auth_token   = "your-npm-auth-token"
```

### Networking

Use existing VCN/subnet or create new ones:

```hcl
# Use existing networking
vcn_id    = "ocid1.vcn.oc1..aaaaaaaa..."
subnet_id = "ocid1.subnet.oc1..aaaaaaaa..."

# Or leave null to create new VCN/subnet
vcn_id    = null
subnet_id = null
```

## Troubleshooting

### ARM64 Compatibility

The media server package must be compatible with ARM64 architecture. If you encounter issues:

1. **Check Node.js version**: Ensure Node.js 20+ is installed
2. **Native modules**: Some npm packages may need recompilation for ARM64
3. **Fallback packages**: The startup script installs fallback packages if the main package fails

### Firewall Issues

If WebRTC connections fail:

```bash
# Check firewall status
sudo firewall-cmd --list-all

# Manually open ports if needed
sudo firewall-cmd --permanent --add-port=32256-65535/udp
sudo firewall-cmd --reload
```

### Service Issues

```bash
# Check service status
sudo systemctl status media-server

# View logs
sudo journalctl -u media-server -f

# Restart service
sudo systemctl restart media-server
```

## Cost Optimization

- **Always Free**: Use up to 4 OCPUs and 24GB RAM for free
- **Flexible Sizing**: Scale OCPUs and memory independently
- **Reserved IP**: Public IP is reserved to maintain consistent endpoint

## Security

- **Security Lists**: Configured for WebRTC media ports (UDP 32256-65535)
- **SSH Access**: Key-based authentication only
- **HTTPS**: Self-signed certificates generated automatically
- **Firewall**: Oracle Linux firewalld configured for required ports

## Cleanup

```bash
# Destroy all resources
terraform destroy
```

This will remove all created resources including the instance, networking, and public IP.
