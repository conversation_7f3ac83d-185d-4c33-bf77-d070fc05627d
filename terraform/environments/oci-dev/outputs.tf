output "media_server_public_ip" {
  description = "Public IP address of the media server"
  value       = module.oci_media_server.media_server_public_ip
}

output "media_server_private_ip" {
  description = "Private IP address of the media server"
  value       = module.oci_media_server.media_server_private_ip
}

output "media_server_url" {
  description = "URL of the media server"
  value       = module.oci_media_server.media_server_url
}

output "instance_id" {
  description = "OCID of the media server instance"
  value       = module.oci_media_server.instance_id
}

output "ssh_command" {
  description = "SSH command to connect to the media server"
  value       = "ssh opc@${module.oci_media_server.media_server_public_ip}"
}

output "artifact_registry_info" {
  description = "Artifact registry information"
  value = {
    repository_name     = module.artifact_registry.repository_name
    repository_endpoint = module.artifact_registry.repository_endpoint_template
    namespace          = module.artifact_registry.namespace
    setup_script       = module.artifact_registry.setup_script_path
    compartment_id     = module.artifact_registry.compartment_id
  }
}

output "artifact_auth_token" {
  description = "Artifact registry authentication token"
  value       = module.artifact_registry.auth_token
  sensitive   = true
}

output "deployment_info" {
  description = "Deployment information"
  value = {
    instance_shape = var.instance_shape
    ocpus         = var.instance_ocpus
    memory_gb     = var.instance_memory_gb
    region        = var.region
    package       = "${var.package_name}-${var.package_version}.tgz"
    artifact_url  = module.artifact_registry.repository_endpoint_template
    architecture  = contains(["VM.Standard.A1.Flex", "VM.Standard.A2.Flex"], var.instance_shape) ? "ARM64" : "x86_64"
  }
}
