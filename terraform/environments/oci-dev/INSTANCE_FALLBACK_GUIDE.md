# OCI Instance Shape Fallback Guide

## 🎯 **Instance Shape Options**

### **Primary: Ampere A1 (ARM64) - Always Free**
```hcl
instance_shape = "VM.Standard.A1.Flex"
```
- **Architecture**: ARM64
- **Cost**: Always Free (up to 4 OCPUs, 24GB RAM)
- **Performance**: Excellent price/performance
- **Availability**: Limited on trial accounts

### **Fallback 1: Ampere A2 (ARM64) - Paid**
```hcl
instance_shape = "VM.Standard.A2.Flex"
```
- **Architecture**: ARM64
- **Cost**: Paid (but very affordable)
- **Performance**: Better than A1
- **Availability**: Usually better than A1

### **Fallback 2: x86 Micro - Always Free**
```hcl
instance_shape = "VM.Standard.E2.1.Micro"
```
- **Architecture**: x86_64
- **Cost**: Always Free (2 instances, 1/8 OCPU, 1GB RAM each)
- **Performance**: Limited but functional
- **Availability**: Usually available

## 🔄 **How to Switch Instance Types**

### **If <PERSON> Fails with "Out of host capacity":**

1. **Try A2 (Recommended)**:
   ```bash
   # Edit terraform.tfvars
   instance_shape = "VM.Standard.A2.Flex"
   
   # Redeploy
   terraform apply
   ```

2. **Try x86 Micro (Last Resort)**:
   ```bash
   # Edit terraform.tfvars
   instance_shape = "VM.Standard.E2.1.Micro"
   
   # Note: Remove OCPU/memory config for micro instances
   # Redeploy
   terraform apply
   ```

## 🌍 **Region Availability Tips**

**Best Regions for Ampere A1/A2:**
- `us-phoenix-1` (Updated default - often better availability)
- `us-ashburn-1` (Original, can be congested)
- `uk-london-1` (Good for EU users)
- `eu-frankfurt-1` (Alternative EU region)

## ⚡ **Quick Troubleshooting**

### **"Out of host capacity" Error:**
1. Try different region (us-phoenix-1)
2. Switch to A2 instance
3. Reduce OCPU count to 1
4. Use x86 micro as last resort

### **"Service limit exceeded" Error:**
- You've hit trial account limits
- Try different region
- Use smaller instance size

### **"Shape not supported" Error:**
- Region doesn't support that shape
- Try different region or shape

## 🎛️ **Configuration Examples**

### **Minimal A1 Configuration:**
```hcl
region             = "us-phoenix-1"
instance_shape     = "VM.Standard.A1.Flex"
instance_ocpus     = 1
instance_memory_gb = 6
```

### **A2 Fallback Configuration:**
```hcl
region             = "us-phoenix-1"
instance_shape     = "VM.Standard.A2.Flex"
instance_ocpus     = 1
instance_memory_gb = 6
```

### **x86 Micro Fallback:**
```hcl
region             = "us-phoenix-1"
instance_shape     = "VM.Standard.E2.1.Micro"
# Note: OCPU/memory are fixed for micro instances
```

## 🔧 **Architecture Detection**

The startup script automatically detects the architecture:
- **ARM64**: A1 and A2 instances
- **x86_64**: E2.1.Micro and other x86 instances

Node.js installation adapts automatically to the detected architecture.

## 💰 **Cost Comparison**

| Instance Type | Architecture | Cost | Always Free | Performance |
|---------------|-------------|------|-------------|-------------|
| A1.Flex       | ARM64       | Free | ✅ (4 OCPU) | Excellent   |
| A2.Flex       | ARM64       | ~$0.01/hr | ❌ | Better      |
| E2.1.Micro    | x86_64      | Free | ✅ (2 inst) | Limited     |

## 🚀 **Recommended Strategy**

1. **Start with A1** in us-phoenix-1
2. **If capacity issues, try A2** (small cost, better availability)
3. **Use x86 micro only for testing** (limited performance)
4. **Monitor OCI status page** for capacity updates

The deployment script will automatically handle architecture differences!
