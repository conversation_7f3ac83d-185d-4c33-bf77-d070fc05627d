variable "compartment_id" {
  description = "OCI Compartment ID"
  type        = string
}

variable "tenancy_ocid" {
  description = "OCID of the tenancy (required for dynamic groups)"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "vcn_id" {
  description = "Existing VCN ID (optional - will create new VCN if not provided)"
  type        = string
  default     = null
}

variable "subnet_id" {
  description = "Existing subnet ID (optional - will create new subnet if not provided)"
  type        = string
  default     = null
}

variable "instance_ocpus" {
  description = "Number of OCPUs for the Ampere A1 instance"
  type        = number
  default     = 1
  validation {
    condition     = var.instance_ocpus >= 1 && var.instance_ocpus <= 4
    error_message = "OCPUs must be between 1 and 4 for A1 instances."
  }
}

variable "instance_memory_gb" {
  description = "Memory in GB for the Ampere A1 instance"
  type        = number
  default     = 6
  validation {
    condition     = var.instance_memory_gb >= 1 && var.instance_memory_gb <= 24
    error_message = "Memory must be between 1 and 24 GB for A1 instances."
  }
}

variable "ssh_public_key" {
  description = "SSH public key for instance access"
  type        = string
}

variable "availability_domain" {
  description = "Specific availability domain to use (optional)"
  type        = string
  default     = null
}

variable "instance_shape" {
  description = "Instance shape to use"
  type        = string
  default     = "VM.Standard.A1.Flex"
}

variable "package_name" {
  description = "Name of the media server package"
  type        = string
  default     = "media-server"
}

variable "package_version" {
  description = "Version of the media server package"
  type        = string
  default     = "latest"
}

variable "artifact_url" {
  description = "OCI Artifact Registry URL for downloading packages"
  type        = string
}

variable "auth_token" {
  description = "OCI Artifact Registry authentication token"
  type        = string
  sensitive   = true
}

variable "jwt_secret" {
  description = "JWT secret key for media server authentication"
  type        = string
  sensitive   = true
  default     = "your-secret-key-change-in-production"
}

variable "mediasoup_min_port" {
  description = "Minimum port for mediasoup RTC"
  type        = number
  default     = 40000
}

variable "mediasoup_max_port" {
  description = "Maximum port for mediasoup RTC"
  type        = number
  default     = 40100
}

variable "freeform_tags" {
  description = "Freeform tags to apply to resources"
  type        = map(string)
  default     = {}
}
