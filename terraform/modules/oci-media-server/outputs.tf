output "media_server_public_ip" {
  description = "Public IP address of the media server"
  value       = oci_core_instance.media_server.public_ip
}

output "media_server_private_ip" {
  description = "Private IP address of the media server"
  value       = oci_core_instance.media_server.private_ip
}

output "media_server_url" {
  description = "URL of the media server"
  value       = "https://${oci_core_instance.media_server.public_ip}:8080"
}

output "instance_id" {
  description = "OCID of the media server instance"
  value       = oci_core_instance.media_server.id
}

output "instance_display_name" {
  description = "Display name of the media server instance"
  value       = oci_core_instance.media_server.display_name
}

output "availability_domain" {
  description = "Availability domain of the media server instance"
  value       = oci_core_instance.media_server.availability_domain
}

output "vcn_id" {
  description = "VCN ID (created or existing)"
  value       = var.vcn_id != null ? var.vcn_id : oci_core_vcn.media_server_vcn[0].id
}

output "subnet_id" {
  description = "Subnet ID (created or existing)"
  value       = var.subnet_id != null ? var.subnet_id : oci_core_subnet.media_server_subnet[0].id
}
