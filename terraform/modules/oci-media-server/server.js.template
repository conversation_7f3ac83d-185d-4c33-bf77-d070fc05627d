#!/usr/bin/env node

require('dotenv').config();
const express = require('express');
const https = require('https');
const fs = require('fs');
const { Server } = require('socket.io');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json());

app.get('/', (req, res) => {
    res.json({
        status: 'success',
        message: 'Media Server - Basic Infrastructure Working!',
        timestamp: new Date().toISOString(),
        note: 'Mediasoup compilation needed for full WebRTC functionality',
        server: {
            platform: process.platform,
            arch: process.arch,
            nodeVersion: process.version,
            uptime: process.uptime()
        }
    });
});

app.get('/health', (req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

app.get('/test-jwt', (req, res) => {
    try {
        const jwt = require('jsonwebtoken');
        const token = jwt.sign({ test: 'data' }, 'test-secret', { expiresIn: '1h' });
        const decoded = jwt.verify(token, 'test-secret');
        res.json({ 
            status: 'success', 
            message: 'JWT working correctly',
            token: token.substring(0, 20) + '...',
            decoded 
        });
    } catch (error) {
        res.status(500).json({ status: 'error', message: error.message });
    }
});

const serverOptions = {
    key: fs.readFileSync('/opt/media-server/certs/server.key'),
    cert: fs.readFileSync('/opt/media-server/certs/server.crt')
};

const server = https.createServer(serverOptions, app);
const io = new Server(server, { cors: { origin: "*", methods: ["GET", "POST"] } });

io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);
    socket.emit('welcome', { 
        message: 'Connected to Media Server', 
        socketId: socket.id,
        timestamp: new Date().toISOString()
    });
    socket.on('disconnect', () => console.log('Client disconnected:', socket.id));
    
    socket.on('test', (data) => {
        console.log('Test message received:', data);
        socket.emit('test-response', {
            message: 'Test successful',
            received: data,
            timestamp: new Date().toISOString()
        });
    });
});

const PORT = process.env.PORT || 8080;
server.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Media Server running on https://0.0.0.0:${PORT}`);
    console.log(`📊 Platform: ${process.platform} ${process.arch}`);
    console.log(`📦 Node.js: ${process.version}`);
    console.log(`🔧 All basic dependencies are working!`);
    console.log(`⚠️  Mediasoup compilation still needed for full functionality`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
