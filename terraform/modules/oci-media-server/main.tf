# OCI Media Server VM for WebRTC-to-RTMP conversion
# Uses Ampere A1 instance for cost-effective ARM64 deployment



# Get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_id
}

# Generate random suffix for unique hostname
resource "random_id" "hostname_suffix" {
  byte_length = 4
}

# Dynamic Group for Media Server Instance
resource "oci_identity_dynamic_group" "media_server_dynamic_group" {
  compartment_id = var.tenancy_ocid
  name           = "${var.name_prefix}-media-server-dynamic-group"
  description    = "Dynamic group for media server instances to access Object Storage"

  matching_rule = "instance.compartment.id = '${var.compartment_id}'"

  freeform_tags = var.freeform_tags
}

# Policy to allow media server instances to access Object Storage
resource "oci_identity_policy" "media_server_policy" {
  compartment_id = var.compartment_id
  name           = "${var.name_prefix}-media-server-policy"
  description    = "Policy allowing media server instances to access Object Storage"

  statements = [
    "allow dynamic-group ${oci_identity_dynamic_group.media_server_dynamic_group.name} to read buckets in compartment id ${var.compartment_id}",
    "allow dynamic-group ${oci_identity_dynamic_group.media_server_dynamic_group.name} to read objects in compartment id ${var.compartment_id}",
    "allow dynamic-group ${oci_identity_dynamic_group.media_server_dynamic_group.name} to manage objects in compartment id ${var.compartment_id} where target.bucket.name='${var.name_prefix}-media-server-repo'"
  ]

  freeform_tags = var.freeform_tags
}

# Get the latest Ubuntu 22.04 LTS image (ARM64 for A1/A2, x86 for others)
data "oci_core_images" "ubuntu" {
  compartment_id           = var.compartment_id
  operating_system         = "Canonical Ubuntu"
  operating_system_version = "22.04"
  shape                    = var.instance_shape
  sort_by                  = "TIMECREATED"
  sort_order               = "DESC"
}

# Create VCN if not provided
resource "oci_core_vcn" "media_server_vcn" {
  count          = var.vcn_id == null ? 1 : 0
  compartment_id = var.compartment_id
  cidr_blocks    = ["10.0.0.0/16"]
  display_name   = "${var.name_prefix}-media-server-vcn"
  dns_label      = "mediaserver"

  freeform_tags = var.freeform_tags
}

# Create Internet Gateway
resource "oci_core_internet_gateway" "media_server_igw" {
  count          = var.vcn_id == null ? 1 : 0
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.media_server_vcn[0].id
  display_name   = "${var.name_prefix}-media-server-igw"
  enabled        = true

  freeform_tags = var.freeform_tags
}

# Create Route Table
resource "oci_core_route_table" "media_server_rt" {
  count          = var.vcn_id == null ? 1 : 0
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.media_server_vcn[0].id
  display_name   = "${var.name_prefix}-media-server-rt"

  route_rules {
    destination       = "0.0.0.0/0"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = oci_core_internet_gateway.media_server_igw[0].id
  }

  freeform_tags = var.freeform_tags
}

# Create Subnet
resource "oci_core_subnet" "media_server_subnet" {
  count               = var.subnet_id == null ? 1 : 0
  compartment_id      = var.compartment_id
  vcn_id              = var.vcn_id != null ? var.vcn_id : oci_core_vcn.media_server_vcn[0].id
  cidr_block          = "********/24"
  display_name        = "${var.name_prefix}-media-server-subnet"
  dns_label           = "mediasubnet"
  route_table_id      = var.vcn_id != null ? null : oci_core_route_table.media_server_rt[0].id
  security_list_ids   = [oci_core_security_list.media_server_seclist.id]
  prohibit_public_ip_on_vnic = false

  freeform_tags = var.freeform_tags
}

# Security List for Media Server
resource "oci_core_security_list" "media_server_seclist" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id != null ? var.vcn_id : oci_core_vcn.media_server_vcn[0].id
  display_name   = "${var.name_prefix}-media-server-seclist"

  # Egress rules - allow all outbound
  egress_security_rules {
    destination = "0.0.0.0/0"
    protocol    = "all"
  }

  # SSH access
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 22
      max = 22
    }
  }

  # HTTPS/HTTP for media server
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 8080
      max = 8081
    }
  }

  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 443
      max = 443
    }
  }

  # WebRTC media ports (UDP) - Mediasoup worker port range
  ingress_security_rules {
    protocol = "17" # UDP
    source   = "0.0.0.0/0"
    udp_options {
      min = 40000
      max = 40100
    }
  }

  # WebRTC media ports (TCP fallback) - Mediasoup worker port range
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 40000
      max = 40100
    }
  }

  freeform_tags = var.freeform_tags
}



# Media Server Instance (Ampere A1)
resource "oci_core_instance" "media_server" {
  compartment_id      = var.compartment_id
  availability_domain = var.availability_domain != null ? var.availability_domain : data.oci_identity_availability_domains.ads.availability_domains[0].name
  display_name        = "${var.name_prefix}-media-server"
  shape               = var.instance_shape

  dynamic "shape_config" {
    for_each = contains(["VM.Standard.A1.Flex", "VM.Standard.A2.Flex"], var.instance_shape) ? [1] : []
    content {
      ocpus         = var.instance_ocpus
      memory_in_gbs = var.instance_memory_gb
    }
  }

  create_vnic_details {
    subnet_id        = var.subnet_id != null ? var.subnet_id : oci_core_subnet.media_server_subnet[0].id
    display_name     = "${var.name_prefix}-media-server-vnic"
    assign_public_ip = true
    hostname_label   = "media-server-${random_id.hostname_suffix.hex}"
  }

  source_details {
    source_type = "image"
    source_id   = data.oci_core_images.ubuntu.images[0].id
  }

  metadata = {
    ssh_authorized_keys = var.ssh_public_key
    user_data = base64encode(templatefile("${path.module}/startup-script-ubuntu.sh", {
      announced_ip         = "AUTO_DETECT"  # Will be detected at runtime
      package_name         = var.package_name
      package_version      = var.package_version
      mediasoup_min_port   = var.mediasoup_min_port
      mediasoup_max_port   = var.mediasoup_max_port
      jwt_secret          = var.jwt_secret
    }))
  }

  freeform_tags = var.freeform_tags

  lifecycle {
    create_before_destroy = true
  }
}


