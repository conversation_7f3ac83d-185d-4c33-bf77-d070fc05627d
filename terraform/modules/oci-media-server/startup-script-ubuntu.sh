#!/bin/bash

# Media Server Startup Script for Ubuntu 22.04 LTS (ARM64)
# This script sets up the media server environment and starts the service

# Don't exit on errors - we want to continue and handle failures gracefully
set +e

# Configure logging
exec > >(tee /var/log/startup-script.log)
exec 2>&1

echo "Starting media server setup on Ubuntu 22.04 LTS ARM64..."

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to run commands with error handling
run_with_error_handling() {
    local command="$1"
    local description="$2"
    local critical="$${3:-false}"

    log_with_timestamp "Starting: $description"
    if eval "$command"; then
        log_with_timestamp "✅ Success: $description"
        return 0
    else
        log_with_timestamp "❌ Failed: $description"
        if [ "$critical" = "true" ]; then
            log_with_timestamp "💥 Critical failure, exiting"
            exit 1
        else
            log_with_timestamp "⚠️  Non-critical failure, continuing"
            return 1
        fi
    fi
}

log_with_timestamp "🚀 Starting Media Server setup on Ubuntu 22.04 LTS (ARM64)..."

# Wait for automatic updates to complete (common on Ubuntu cloud instances)
log_with_timestamp "Waiting for automatic updates to complete..."
while fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1 || fuser /var/lib/apt/lists/lock >/dev/null 2>&1; do
    log_with_timestamp "Waiting for apt lock to be released..."
    sleep 10
done

# Update system packages
log_with_timestamp "Updating system packages..."
run_with_error_handling "apt update && apt upgrade -y" "System package update" true

# Install essential packages
log_with_timestamp "Installing essential packages..."
run_with_error_handling "apt install -y curl wget gnupg software-properties-common build-essential openssl tar gzip jq" "Essential packages installation" true

# Install Node.js 20 from NodeSource repository
log_with_timestamp "Installing Node.js 20..."

# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -

# Install Node.js
run_with_error_handling "apt install -y nodejs" "Node.js installation" true

# Verify Node.js installation
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    log_with_timestamp "✅ Node.js installed: $NODE_VERSION"
    log_with_timestamp "✅ npm installed: $NPM_VERSION"
else
    log_with_timestamp "❌ Node.js installation failed"
    exit 1
fi

# Install build tools for native modules (mediasoup requires compilation)
log_with_timestamp "Installing build tools for native module compilation..."
run_with_error_handling "apt install -y build-essential python3 python3-pip make g++ cmake libtool pkg-config" "Build tools installation" true

# Install FFmpeg (readily available on Ubuntu)
log_with_timestamp "Installing FFmpeg for ARM64..."
run_with_error_handling "apt install -y ffmpeg" "FFmpeg installation" true

# Verify FFmpeg installation
if command -v ffmpeg &> /dev/null; then
    log_with_timestamp "✅ FFmpeg installed successfully: $(ffmpeg -version | head -1)"
else
    log_with_timestamp "❌ FFmpeg installation failed"
    exit 1
fi

# Install Python build dependencies for mediasoup
log_with_timestamp "Installing Python build dependencies for mediasoup..."
run_with_error_handling "pip3 install setuptools wheel" "Install setuptools and wheel" false
run_with_error_handling "pip3 install scikit-build" "Install scikit-build" false
run_with_error_handling "pip3 install invoke" "Install invoke" false
run_with_error_handling "pip3 install 'meson>=0.61.0,<1.1.0'" "Install compatible meson version" false
run_with_error_handling "pip3 install ninja" "Install ninja" false

# Install OCI CLI for Object Storage access
log_with_timestamp "Installing OCI CLI..."
curl -L https://raw.githubusercontent.com/oracle/oci-cli/master/scripts/install/install.sh -o /tmp/oci-install.sh
run_with_error_handling "bash /tmp/oci-install.sh --accept-all-defaults" "OCI CLI installation" true

# Add OCI CLI to PATH
export PATH="/root/bin:$PATH"
echo 'export PATH="/root/bin:$PATH"' >> /root/.bashrc

# Verify OCI CLI installation
if command -v oci &> /dev/null; then
    log_with_timestamp "✅ OCI CLI installed successfully: $(oci --version)"
else
    log_with_timestamp "❌ OCI CLI installation failed"
    exit 1
fi

# Create media server directory
mkdir -p /opt/media-server
cd /opt/media-server

# Create SSL certificates directory
mkdir -p /opt/media-server/ssl

# Auto-detect public IP if needed
if [ "${announced_ip}" = "AUTO_DETECT" ]; then
    ANNOUNCED_IP=$(curl -s http://***************/opc/v1/vnics/ | jq -r '.[0].publicIp // empty')
    if [ -z "$ANNOUNCED_IP" ]; then
        ANNOUNCED_IP=$(curl -s https://ipinfo.io/ip)
    fi
else
    ANNOUNCED_IP="${announced_ip}"
fi

# Generate self-signed SSL certificate for HTTPS
openssl req -x509 -newkey rsa:4096 -keyout /opt/media-server/ssl/key.pem -out /opt/media-server/ssl/cert.pem -days 365 -nodes -subj "/C=US/ST=CA/L=SF/O=WebRTC/CN=$ANNOUNCED_IP"

# Set proper permissions for SSL certificates
chmod 644 /opt/media-server/ssl/cert.pem
chmod 600 /opt/media-server/ssl/key.pem

# Configure OCI CLI to use instance principal authentication
log_with_timestamp "Configuring OCI CLI for instance principal authentication..."
export OCI_CLI_AUTH=instance_principal

# Download and extract media server package from OCI Object Storage
echo "Downloading media server package from OCI Object Storage..."

# Package details
PACKAGE_NAME="${package_name}"
PACKAGE_VERSION="${package_version}"
BUCKET_NAME="sai-platform-oci-dev-media-server-repo"

# Try to download clean package first (without node_modules for ARM64 compatibility)
CLEAN_PACKAGE_FILE="media-server-$${PACKAGE_VERSION}-clean.tgz"
REGULAR_PACKAGE_FILE="media-server-$${PACKAGE_VERSION}.tgz"

echo "Attempting to download clean package: $${CLEAN_PACKAGE_FILE} from bucket: $${BUCKET_NAME}"

# Try clean package first, fallback to regular package
log_with_timestamp "Downloading clean package (without node_modules) from private Object Storage bucket..."
if oci os object get --bucket-name "$${BUCKET_NAME}" --name "$${CLEAN_PACKAGE_FILE}" --file "$${CLEAN_PACKAGE_FILE}"; then
    PACKAGE_FILE="$${CLEAN_PACKAGE_FILE}"
    log_with_timestamp "✅ Clean package downloaded successfully"
elif oci os object get --bucket-name "$${BUCKET_NAME}" --name "$${REGULAR_PACKAGE_FILE}" --file "$${REGULAR_PACKAGE_FILE}"; then
    PACKAGE_FILE="$${REGULAR_PACKAGE_FILE}"
    log_with_timestamp "⚠️  Regular package downloaded - will need to rebuild node_modules for ARM64"
else
    log_with_timestamp "❌ Failed to download any package version"
    exit 1
fi

if [ -f "$${PACKAGE_FILE}" ]; then
    echo "✅ Package downloaded successfully: $${PACKAGE_FILE}"
    
    # Extract the package
    echo "Extracting package..."
    tar -xzf "$${PACKAGE_FILE}"

    # Handle different package structures
    if [ -d "media-server" ]; then
        # Move all files including hidden ones from subdirectory
        shopt -s dotglob
        mv media-server/* .
        rmdir media-server
        echo "✅ Package extracted successfully from subdirectory"
    elif [ -f "server.js" ] && [ -f "package.json" ]; then
        # Files extracted directly to current directory
        echo "✅ Package extracted successfully (direct extraction)"
    else
        echo "❌ Package structure unexpected - missing server.js or package.json"
        echo "Contents of extracted package:"
        ls -la
        exit 1
    fi
    
    # Cleanup downloaded file
    rm -f "$${PACKAGE_FILE}"
else
    echo "❌ Failed to download package from OCI Object Storage"
    exit 1
fi

# Verify we have the actual media server files
if [ ! -f "server.js" ]; then
    log_with_timestamp "❌ server.js not found - package extraction may have failed"
    exit 1
fi

if [ ! -f "package.json" ]; then
    log_with_timestamp "❌ package.json not found - package extraction may have failed"
    exit 1
fi

log_with_timestamp "✅ Media server files verified"

# Remove any existing node_modules to ensure clean installation
if [ -d "node_modules" ]; then
    log_with_timestamp "Removing existing node_modules for clean installation..."
    rm -rf node_modules
fi

# Install npm dependencies (will try to download prebuilt mediasoup binary first)
log_with_timestamp "Installing npm dependencies (with prebuilt binary support)..."
run_with_error_handling "npm install" "Installing npm dependencies" true

# Verify mediasoup installation
if [ -d "node_modules/mediasoup" ]; then
    log_with_timestamp "✅ mediasoup installed successfully for ARM64"
else
    log_with_timestamp "❌ mediasoup installation failed"
    exit 1
fi

log_with_timestamp "✅ All dependencies installed successfully"

# Create environment file
cat > .env << EOF
# Media Server Configuration
JWT_SECRET=${jwt_secret}
LISTEN_IP=0.0.0.0
ANNOUNCED_IP=${announced_ip}
HTTPS_CERT_FULLCHAIN=/opt/media-server/ssl/cert.pem
HTTPS_CERT_PRIVKEY=/opt/media-server/ssl/key.pem
MEDIASOUP_MIN_PORT=${mediasoup_min_port}
MEDIASOUP_MAX_PORT=${mediasoup_max_port}

# WebRTC Port Configuration (must match iptables and Security List)
RTC_MIN_PORT=40000
RTC_MAX_PORT=40100
WEBRTC_PORT_RANGE_MIN=40000
WEBRTC_PORT_RANGE_MAX=40100
WEBRTC_ENABLE_UDP=true
WEBRTC_ENABLE_TCP=true
WEBRTC_PREFER_UDP=true

# Recording Configuration
RECORDING_IP=127.0.0.1

# Video Quality Configuration for Twitch
MAX_BITRATE=3200000
MAX_FRAMERATE=30.0
START_BITRATE=3200
EOF

log_with_timestamp "✅ Environment configuration created"

# Configure iptables to allow media server port
log_with_timestamp "Configuring iptables to allow port 8081..."
iptables -I INPUT 4 -p tcp --dport 8081 -j ACCEPT
log_with_timestamp "✅ iptables rule added for port 8081"

# Configure iptables to allow WebRTC media ports (position 6 is critical for proper traffic flow)
log_with_timestamp "Configuring iptables to allow WebRTC media ports (40000-40100)..."
iptables -I INPUT 6 -p tcp --dport 40000:40100 -j ACCEPT
iptables -I INPUT 6 -p udp --dport 40000:40100 -j ACCEPT
log_with_timestamp "✅ iptables rules added for WebRTC media ports at position 6"

# Save iptables rules to persist across reboots
log_with_timestamp "Saving iptables rules..."
if command -v netfilter-persistent &> /dev/null; then
    netfilter-persistent save
    log_with_timestamp "✅ iptables rules saved with netfilter-persistent"
elif command -v iptables-save &> /dev/null; then
    iptables-save > /etc/iptables/rules.v4
    log_with_timestamp "✅ iptables rules saved to /etc/iptables/rules.v4"
else
    log_with_timestamp "⚠️ Could not find netfilter-persistent or iptables-save, rules may not persist"
fi

# Create systemd service
cat > /etc/systemd/system/media-server.service << EOF
[Unit]
Description=Media Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/media-server
EnvironmentFile=/opt/media-server/.env
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl daemon-reload
systemctl enable media-server
systemctl start media-server

log_with_timestamp "✅ Media server service created and started"

# Check service status
sleep 5
if systemctl is-active --quiet media-server; then
    log_with_timestamp "✅ Media server is running successfully"
else
    log_with_timestamp "❌ Media server failed to start"
    systemctl status media-server
    exit 1
fi

log_with_timestamp "🎉 Media server setup completed successfully on Ubuntu 22.04 LTS ARM64!"
