#!/bin/bash

# Upload Media Server Package to OCI Artifact Registry
# This script packages the media server and uploads it as a versioned tgz

set -e

# Configuration from Terraform
REPOSITORY_ENDPOINT="${repository_endpoint}"
NAMESPACE="${namespace}"
REPOSITORY_ID="${repository_id}"
AUTH_TOKEN="${auth_token}"
USER_OCID="${user_ocid}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "$${BLUE}[INFO]$${NC} $1"
}

log_success() {
    echo -e "$${GREEN}[SUCCESS]$${NC} $1"
}

log_warning() {
    echo -e "$${YELLOW}[WARNING]$${NC} $1"
}

log_error() {
    echo -e "$${RED}[ERROR]$${NC} $1"
}

# Function to get version from package.json
get_version() {
    if [ -f "package.json" ]; then
        node -p "require('./package.json').version" 2>/dev/null || echo "1.0.0"
    else
        echo "1.0.0"
    fi
}

# Function to create package
create_package() {
    local version=$1
    local package_name="media-server-$${version}.tgz"
    
    log_info "Creating package: $${package_name}"
    
    # Create temporary directory for packaging
    local temp_dir=$(mktemp -d)
    local package_dir="$${temp_dir}/media-server"
    
    # Copy media server files
    mkdir -p "$${package_dir}"
    
    # Copy essential files
    cp -r server.js config.js config.production.js package.json "$${package_dir}/" 2>/dev/null || {
        log_error "Failed to copy media server files. Make sure you're in the media-server directory."
        rm -rf "$${temp_dir}"
        exit 1
    }
    
    # Copy node_modules if they exist (for bundled deployment)
    if [ -d "node_modules" ]; then
        log_info "Including node_modules in package..."
        cp -r node_modules "$${package_dir}/"
    fi
    
    # Create tarball
    cd "$${temp_dir}"
    tar -czf "$${package_name}" media-server/
    
    # Move to original directory
    mv "$${package_name}" "$${OLDPWD}/"
    cd "$${OLDPWD}"
    
    # Cleanup
    rm -rf "$${temp_dir}"
    
    echo "$${package_name}"
}

# Function to upload package
upload_package() {
    local package_file=$1
    local version=$2
    
    log_info "Uploading $${package_file} to OCI Artifact Registry..."
    
    # Upload using OCI CLI
    oci artifacts generic artifact upload-by-path \
        --repository-id "$${REPOSITORY_ID}" \
        --artifact-path "media-server-$${version}.tgz" \
        --content-body "$${package_file}" \
        --artifact-version "$${version}" \
        --auth "$${AUTH_TOKEN}" || {
        log_error "Failed to upload package to OCI Artifact Registry"
        return 1
    }
    
    log_success "Package uploaded successfully"
    log_info "Download URL: $${REPOSITORY_ENDPOINT}/media-server-$${version}.tgz"
}

# Main function
main() {
    local version_override=$1
    
    log_info "OCI Artifact Registry Package Upload"
    
    # Check if we're in the right directory
    if [ ! -f "server.js" ] || [ ! -f "package.json" ]; then
        log_error "Please run this script from the media-server directory"
        exit 1
    fi
    
    # Get version
    local version
    if [ -n "$${version_override}" ]; then
        version="$${version_override}"
        log_info "Using provided version: $${version}"
    else
        version=$(get_version)
        log_info "Using version from package.json: $${version}"
    fi
    
    # Create package
    local package_file
    package_file=$(create_package "$${version}")
    
    if [ ! -f "$${package_file}" ]; then
        log_error "Failed to create package"
        exit 1
    fi
    
    log_success "Package created: $${package_file}"
    
    # Upload package
    upload_package "$${package_file}" "$${version}"
    
    # Cleanup local package file
    rm -f "$${package_file}"
    
    log_success "Upload completed successfully"
    log_info "Package: media-server-$${version}.tgz"
    log_info "Repository: $${REPOSITORY_ENDPOINT}"
}

# Show usage if no arguments and not in media-server directory
if [ $# -eq 0 ] && ([ ! -f "server.js" ] || [ ! -f "package.json" ]); then
    echo "Usage: $0 [version]"
    echo ""
    echo "Upload media server package to OCI Artifact Registry"
    echo ""
    echo "Arguments:"
    echo "  version    Optional version override (uses package.json version if not provided)"
    echo ""
    echo "Examples:"
    echo "  $0                # Use version from package.json"
    echo "  $0 1.2.3          # Use specific version"
    echo ""
    echo "Note: Run this script from the media-server directory"
    exit 1
fi

# Run main function
main "$@"
