variable "compartment_id" {
  description = "OCI Compartment ID"
  type        = string
}

variable "user_ocid" {
  description = "OCI User OCID for auth token creation"
  type        = string
}

variable "region" {
  description = "OCI Region"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "freeform_tags" {
  description = "Freeform tags to apply to resources"
  type        = map(string)
  default     = {}
}
