#!/bin/bash

# Setup OCI Artifact Repository and Upload Media Server Package
# This script creates the repository and uploads packages

set -e

# Configuration from Terraform
COMPARTMENT_ID="ocid1.tenancy.oc1..aaaaaaaa3zo55xe333wrrnju5agpybkwv7t7tuuiovd4dgqg72ahjskra4ma"
REPOSITORY_NAME="sai-platform-oci-dev-media-server-repo"
NAMESPACE="idqklxxi9ug5"
AUTH_TOKEN="]iUKdMz_TbQ{Ccz#z_93"
USER_OCID="ocid1.user.oc1..aaaaaaaagja627i3iql26afptos4isweheblawg6cj3tro3jmmdjrkh436iq"
REGION="us-ashburn-1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to create repository
create_repository() {
    log_info "Creating artifact repository: ${REPOSITORY_NAME}"
    
    # Check if repository already exists
    if oci artifacts repository list --compartment-id "${COMPARTMENT_ID}" --display-name "${REPOSITORY_NAME}" --query 'data[0].id' --raw-output 2>/dev/null | grep -q "ocid"; then
        log_warning "Repository ${REPOSITORY_NAME} already exists"
        return 0
    fi
    
    # Create the repository
    local repo_id
    if repo_id=$(oci artifacts repository create-generic-repository \
        --compartment-id "${COMPARTMENT_ID}" \
        --display-name "${REPOSITORY_NAME}" \
        --is-immutable false \
        --wait-for-state AVAILABLE \
        --query 'data.id' \
        --raw-output); then
        log_success "Repository created with ID: ${repo_id}"
        echo "${repo_id}"
    else
        log_error "Failed to create repository"
        return 1
    fi
}

# Function to get repository ID
get_repository_id() {
    oci artifacts repository list \
        --compartment-id "${COMPARTMENT_ID}" \
        --display-name "${REPOSITORY_NAME}" \
        --query 'data[0].id' \
        --raw-output 2>/dev/null || echo ""
}

# Function to upload package
upload_package() {
    local package_file=$1
    local version=$2
    local repo_id=$3
    
    if [ ! -f "${package_file}" ]; then
        log_error "Package file not found: ${package_file}"
        return 1
    fi
    
    log_info "Uploading ${package_file} to repository..."
    
    # Upload using OCI CLI
    if oci artifacts generic artifact upload-by-path \
        --repository-id "${repo_id}" \
        --artifact-path "media-server-${version}.tgz" \
        --content-body "${package_file}" \
        --artifact-version "${version}"; then
        log_success "Package uploaded successfully"
        log_info "Download URL: https://objectstorage.${REGION}.oraclecloud.com/n/${NAMESPACE}/b/${REPOSITORY_NAME}/o/media-server-${version}.tgz"
    else
        log_error "Failed to upload package"
        return 1
    fi
}

# Function to list artifacts
list_artifacts() {
    local repo_id=$1
    
    log_info "Listing artifacts in repository..."
    oci artifacts generic artifact list --repository-id "${repo_id}" --query 'data[*].{Path:"artifact-path",Version:version,Size:"size-in-bytes"}' --output table
}

# Main function
main() {
    local action=${1:-"create"}
    local package_file=$2
    local version=$3
    
    log_info "OCI Artifact Repository Setup"
    log_info "Repository: ${REPOSITORY_NAME}"
    log_info "Compartment: ${COMPARTMENT_ID}"
    log_info "Action: ${action}"
    
    case $action in
        "create")
            create_repository
            ;;
        "upload")
            if [ -z "${package_file}" ] || [ -z "${version}" ]; then
                log_error "Usage: $0 upload <package_file> <version>"
                exit 1
            fi
            
            local repo_id
            repo_id=$(get_repository_id)
            if [ -z "${repo_id}" ]; then
                log_error "Repository not found. Create it first with: $0 create"
                exit 1
            fi
            
            upload_package "${package_file}" "${version}" "${repo_id}"
            ;;
        "list")
            local repo_id
            repo_id=$(get_repository_id)
            if [ -z "${repo_id}" ]; then
                log_error "Repository not found"
                exit 1
            fi
            
            list_artifacts "${repo_id}"
            ;;
        "info")
            local repo_id
            repo_id=$(get_repository_id)
            if [ -n "${repo_id}" ]; then
                log_success "Repository exists with ID: ${repo_id}"
                log_info "Repository Name: ${REPOSITORY_NAME}"
                log_info "Namespace: ${NAMESPACE}"
                log_info "Region: ${REGION}"
                log_info "Download URL Template: https://objectstorage.${REGION}.oraclecloud.com/n/${NAMESPACE}/b/${REPOSITORY_NAME}/o/media-server-{version}.tgz"
            else
                log_warning "Repository does not exist. Create it with: $0 create"
            fi
            ;;
        *)
            echo "Usage: $0 [create|upload|list|info] [package_file] [version]"
            echo ""
            echo "Commands:"
            echo "  create                    Create the artifact repository"
            echo "  upload <file> <version>   Upload a package to the repository"
            echo "  list                      List artifacts in the repository"
            echo "  info                      Show repository information"
            echo ""
            echo "Examples:"
            echo "  $0 create"
            echo "  $0 upload dist/media-server-1.0.0.tgz 1.0.0"
            echo "  $0 list"
            echo "  $0 info"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
