# OCI Object Storage for Media Server Package Storage
# Uses Object Storage bucket for package storage (simpler and more reliable than artifact registry)

# Get current namespace
data "oci_objectstorage_namespace" "current" {
  compartment_id = var.compartment_id
}

# Create Object Storage bucket for media server packages
resource "oci_objectstorage_bucket" "media_server_repo" {
  compartment_id = var.compartment_id
  name           = "${var.name_prefix}-media-server-repo"
  namespace      = data.oci_objectstorage_namespace.current.namespace

  # Private bucket - no public access (default)

  # Enable versioning for package management
  versioning = "Enabled"

  freeform_tags = {
    "Application" = "sai-platform"
    "Environment" = var.environment
    "ManagedBy"   = "terraform"
    "Purpose"     = "media-server-packages"
  }
}

# Create Auth Token for Repository Access
resource "oci_identity_auth_token" "artifact_auth_token" {
  description = "Auth token for ${var.name_prefix} media server artifact repository"
  user_id     = var.user_ocid
}

# Create a local script to create repository and upload packages
resource "local_file" "setup_script" {
  filename = "${path.module}/setup-repository.sh"
  content = templatefile("${path.module}/setup-repository.sh.tpl", {
    compartment_id = var.compartment_id
    name_prefix    = var.name_prefix
    namespace      = data.oci_objectstorage_namespace.current.namespace
    auth_token     = oci_identity_auth_token.artifact_auth_token.token
    user_ocid      = var.user_ocid
    region         = var.region
  })

  file_permission = "0755"
}
