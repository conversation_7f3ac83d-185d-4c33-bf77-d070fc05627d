output "repository_name" {
  description = "Artifact repository name"
  value       = "${var.name_prefix}-media-server-repo"
}

output "repository_id" {
  description = "Object Storage bucket OCID"
  value       = oci_objectstorage_bucket.media_server_repo.id
}

output "namespace" {
  description = "OCI Object Storage namespace"
  value       = data.oci_objectstorage_namespace.current.namespace
}

output "auth_token" {
  description = "Authentication token for repository access"
  value       = oci_identity_auth_token.artifact_auth_token.token
  sensitive   = true
}

output "setup_script_path" {
  description = "Path to the repository setup script"
  value       = local_file.setup_script.filename
}

output "repository_endpoint_template" {
  description = "Repository endpoint for downloading packages"
  value       = "https://objectstorage.${var.region}.oraclecloud.com/n/${data.oci_objectstorage_namespace.current.namespace}/b/${var.name_prefix}-media-server-repo/o"
}

output "compartment_id" {
  description = "Compartment ID for repository creation"
  value       = var.compartment_id
}
