{"name": "@sai/media-server", "description": "WebRTC to RTMP Media Server", "version": "1.0.5", "main": "server.js", "bin": {"sai-media-server": "./server.js"}, "scripts": {"start": "node server.js"}, "files": ["server.js", "config.production.js"], "dependencies": {"browserify": "^16.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.0.0", "ffmpeg-static": "^4.0.0", "jsonwebtoken": "^9.0.2", "mediasoup": "^3.19.2", "mediasoup-client": "~3.7.0", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "socket.io-promise": "^1.0.0"}, "devDependencies": {"eslint": "^6.0.0", "prettier": "~2.0.0"}, "publishConfig": {"registry": "https://us-central1-npm.pkg.dev/switcher-ai-469818/sai-platform-dev-npm/"}}