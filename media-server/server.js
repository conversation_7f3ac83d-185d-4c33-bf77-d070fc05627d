// Load environment variables
const dotenvResult = require('dotenv').config();
console.log('🔧 Dotenv result:', dotenvResult.error ? `Error: ${dotenvResult.error}` : 'Success');
console.log('🔧 All environment variables:', {
  JWT_SECRET: process.env.JWT_SECRET ? `${process.env.JWT_SECRET.substring(0, 15)}...` : 'NOT SET',
  PORT: process.env.PORT,
  NODE_ENV: process.env.NODE_ENV
});

const express = require('express');
const http = require('http');
const https = require('https');
const fs = require('fs');
const socketIo = require('socket.io');
const cors = require('cors');
const mediasoup = require('mediasoup');
const { spawn } = require('child_process');
const jwt = require('jsonwebtoken');

const CONFIG = process.env.NODE_ENV === 'production'
  ? require('./config.production')
  : require('./config');

const app = express();

// Create server (HTTP or HTTPS based on environment)
let server;
if (process.env.ENABLE_HTTPS === 'true' && process.env.SSL_CERT_PATH && process.env.SSL_KEY_PATH) {
  console.log('🔒 Starting HTTPS server');
  const options = {
    cert: fs.readFileSync(process.env.SSL_CERT_PATH),
    key: fs.readFileSync(process.env.SSL_KEY_PATH)
  };
  server = https.createServer(options, app);
} else {
  console.log('🌐 Starting HTTP server');
  server = http.createServer(app);
}

const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Apply authentication middleware
io.use(authenticateSocket);

app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    status: 'ok',
    service: 'media-server',
    timestamp: new Date().toISOString(),
    connections: io.engine.clientsCount
  });
});

// Global mediasoup objects - simplified for direct connections
let worker;
let router;
let webRtcTransport;
let producer;
let audioProducer;
let rtpTransport;
let audioRtpTransport;
let consumer;
let audioConsumer;
let ffmpegProcess;
let keyframeInterval;
let statsInterval;
let audioStatsInterval;
let rtmpUrl = null; // Store RTMP URL provided by frontend

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
console.log('🔑 JWT_SECRET loaded:', JWT_SECRET ? `${JWT_SECRET.substring(0, 10)}...` : 'NOT SET');

// Store authenticated user sessions
const authenticatedSockets = new Map(); // socketId -> { userId, email, name }

// JWT verification function
function verifyJWT(token) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    return {
      userId: decoded.sub,
      email: decoded.email,
      name: decoded.name
    };
  } catch (error) {
    console.error('❌ JWT verification failed:', error.message);
    console.error('🔍 Token preview:', token.substring(0, 50) + '...');
    return null;
  }
}

// Authentication middleware for socket connections
function authenticateSocket(socket, next) {
  try {
    // Extract token from handshake
    const token = socket.handshake.auth?.token ||
                  socket.handshake.headers?.authorization?.replace('Bearer ', '');

    if (!token) {
      console.warn(`🔒 Socket ${socket.id} connected without token`);
      return next(new Error('Authentication required'));
    }

    // Verify JWT token
    const user = verifyJWT(token);
    if (!user) {
      console.warn(`🔒 Socket ${socket.id} authentication failed`);
      return next(new Error('Invalid token'));
    }

    // Store user info for this socket
    authenticatedSockets.set(socket.id, user);
    console.log(`✅ Authenticated user ${user.userId} (${user.email}) connected: ${socket.id}`);

    next();
  } catch (error) {
    console.error(`❌ Authentication error for socket ${socket.id}:`, error.message);
    next(new Error('Authentication failed'));
  }
}

// Initialize mediasoup
async function initializeMediasoup() {
  try {
    console.log('📊 Mediasoup configuration:', {
      environment: process.env.NODE_ENV,
      plainTransportIp: CONFIG.mediasoup.plainTransport.listenIp,
      recordingIp: CONFIG.mediasoup.recording.ip,
      audioPorts: `${CONFIG.mediasoup.recording.audioPort}/${CONFIG.mediasoup.recording.audioPortRtcp}`,
      videoPorts: `${CONFIG.mediasoup.recording.videoPort}/${CONFIG.mediasoup.recording.videoPortRtcp}`
    });

    // Create worker
    worker = await mediasoup.createWorker(CONFIG.mediasoup.worker);
    console.log('✅ Mediasoup worker created [pid:%d]', worker.pid);

    worker.on('died', () => {
      console.error('❌ Mediasoup worker died, exiting...');
      process.exit(1);
    });

    // Create router with both audio and video codecs for RTMP compatibility
    router = await worker.createRouter(CONFIG.mediasoup.router);
    console.log('✅ Mediasoup router created');

    return true;
  } catch (error) {
    console.error('❌ Failed to initialize mediasoup:', error);
    return false;
  }
}

// Socket.io connection handling
io.on('connection', (socket) => {
  const user = authenticatedSockets.get(socket.id);
  console.log('🔌 Authenticated client connected:', socket.id, `(User: ${user.userId})`);
  console.log('📊 Total connections:', io.engine.clientsCount);

  // Handle WebRTC transport creation
  socket.on('get-transport-info', async () => {
    console.log('📡 Client requested transport info');
    try {
      if (!router) {
        console.error('❌ Router not initialized');
        socket.emit('error', { message: 'Router not initialized' });
        return;
      }

      // Create WebRTC transport
      webRtcTransport = await router.createWebRtcTransport(CONFIG.mediasoup.webrtcTransport);

      const transportInfo = {
        id: webRtcTransport.id,
        iceParameters: webRtcTransport.iceParameters,
        iceCandidates: webRtcTransport.iceCandidates,
        dtlsParameters: webRtcTransport.dtlsParameters,
        routerRtpCapabilities: router.rtpCapabilities,
      };

      socket.emit('transport-info', transportInfo);
      console.log('✅ WebRTC transport created and sent to client');
    } catch (error) {
      console.error('❌ Failed to create transport:', error);
      socket.emit('error', { message: 'Failed to create transport' });
    }
  });

  // Handle transport connection
  socket.on('connect-transport', async ({ dtlsParameters }) => {
    console.log('🔗 Client connecting transport');
    try {
      if (!webRtcTransport) {
        console.error('❌ Transport not created');
        socket.emit('error', { message: 'Transport not created' });
        return;
      }

      await webRtcTransport.connect({ dtlsParameters });
      socket.emit('transport-connected');
      console.log('✅ WebRTC transport connected');
    } catch (error) {
      console.error('❌ Failed to connect transport:', error);
      socket.emit('error', { message: 'Failed to connect transport' });
    }
  });

  // Handle producer creation
  socket.on('create-producer', async ({ kind, rtpParameters }) => {
    console.log('🎬 Client creating producer, kind:', kind);
    try {
      if (!webRtcTransport) {
        console.error('❌ Transport not connected');
        socket.emit('error', { message: 'Transport not connected' });
        return;
      }

      // Clean up existing producer of the same kind first
      if (kind === 'video' && producer) {
        console.log('🧹 Cleaning up existing video producer:', producer.id);
        producer.close();
        producer = null;
      } else if (kind === 'audio' && audioProducer) {
        console.log('🧹 Cleaning up existing audio producer:', audioProducer.id);
        audioProducer.close();
        audioProducer = null;
      }

      const newProducer = await webRtcTransport.produce({ kind, rtpParameters });

      // Store producers by kind
      if (kind === 'video') {
        producer = newProducer;
        console.log('✅ Video producer created:', producer.id);
      } else if (kind === 'audio') {
        audioProducer = newProducer;
        console.log('✅ Audio producer created:', audioProducer.id);
      }

      socket.emit('producer-created', { producerId: newProducer.id });
      console.log('📊 Producer details:', {
        id: newProducer.id,
        kind: newProducer.kind,
        paused: newProducer.paused,
        rtpParameters: {
          codecs: rtpParameters.codecs?.map(c => ({ mimeType: c.mimeType, clockRate: c.clockRate })),
          headerExtensions: rtpParameters.headerExtensions?.length,
          encodings: rtpParameters.encodings?.length
        }
      });

      // Immediately check producer stats to see if data is flowing
      setTimeout(async () => {
        try {
          const stats = await newProducer.getStats();
          console.log(`📊 ${kind} producer immediate stats:`, stats);
        } catch (error) {
          console.log(`❌ Failed to get ${kind} producer immediate stats:`, error.message);
        }
      }, 1000);

      // Monitor producer stats
      if (kind === 'video') {
        // Clear existing video stats interval if any
        if (statsInterval) {
          clearInterval(statsInterval);
        }

        statsInterval = setInterval(async () => {
          try {
            if (producer && !producer.closed) {
              const stats = await producer.getStats();
              console.log('📊 Video producer stats:', stats);
            }
          } catch (error) {
            console.log('⚠️ Failed to get video producer stats:', error);
            // Clear interval if producer is no longer valid
            if (statsInterval) {
              clearInterval(statsInterval);
              statsInterval = null;
            }
          }
        }, 5000);
      } else if (kind === 'audio') {
        // Clear existing audio stats interval if any
        if (audioStatsInterval) {
          clearInterval(audioStatsInterval);
        }

        audioStatsInterval = setInterval(async () => {
          try {
            if (audioProducer && !audioProducer.closed) {
              const stats = await audioProducer.getStats();
              console.log('📊 Audio producer stats:', stats);
            }
          } catch (error) {
            console.log('⚠️ Failed to get audio producer stats:', error);
            // Clear interval if producer is no longer valid
            if (audioStatsInterval) {
              clearInterval(audioStatsInterval);
              audioStatsInterval = null;
            }
          }
        }, 5000);
      }

      newProducer.on('close', () => {
        if (kind === 'video' && statsInterval) {
          clearInterval(statsInterval);
          statsInterval = null;
        } else if (kind === 'audio' && audioStatsInterval) {
          clearInterval(audioStatsInterval);
          audioStatsInterval = null;
        }
        console.log('🔌 Producer closed:', kind);
      });

      // Auto-start RTMP streaming when video producer is created (if RTMP URL is configured)
      if (kind === 'video') {
        if (rtmpUrl) {
          console.log('🎬 Auto-starting RTMP streaming for video producer...');
          try {
            await startRTMPStreaming();
            console.log('✅ RTMP streaming auto-started successfully');
            socket.emit('streaming-status', { isStreaming: true });
          } catch (error) {
            console.error('❌ Failed to auto-start RTMP streaming:', error);
            socket.emit('error', { message: 'Failed to start RTMP streaming: ' + error.message });
          }
        } else {
          console.log('⚠️ Video producer created but RTMP URL not configured. Streaming will not auto-start.');
          socket.emit('rtmp-url-required', { message: 'RTMP URL required to start streaming' });
        }
      }
    } catch (error) {
      console.error('❌ Failed to create producer:', error);
      socket.emit('error', { message: 'Failed to create producer' });
    }
  });

  // Handle RTMP URL configuration
  socket.on('set-rtmp-url', ({ url }) => {
    const user = authenticatedSockets.get(socket.id);
    if (!user) {
      console.warn('🔒 Unauthorized attempt to set RTMP URL');
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    console.log(`🔧 User ${user.userId} (${user.email}) setting RTMP URL:`, url);
    rtmpUrl = url;
    socket.emit('rtmp-url-set', { success: true, url });
    console.log('✅ RTMP URL configured:', url);
  });

  // Handle manual start RTMP request
  socket.on('start-rtmp', async () => {
    const user = authenticatedSockets.get(socket.id);
    if (!user) {
      console.warn('🔒 Unauthorized attempt to start RTMP streaming');
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    console.log(`🚀 User ${user.userId} (${user.email}) requested to start RTMP streaming`);
    try {
      if (!rtmpUrl) {
        socket.emit('error', { message: 'RTMP URL not configured. Please set RTMP URL first.' });
        return;
      }

      if (!producer) {
        socket.emit('error', { message: 'No video producer available. Please start video first.' });
        return;
      }

      await startRTMPStreaming();
      const isStreaming = ffmpegProcess !== null && !ffmpegProcess.killed;
      socket.emit('rtmp-status', {
        streaming: isStreaming,
        message: isStreaming ? 'RTMP stream started' : 'Failed to start RTMP stream'
      });
      socket.emit('streaming-status', { isStreaming });
      console.log(`📡 RTMP streaming started by user ${user.userId}, sent status:`, isStreaming);
    } catch (error) {
      console.error('❌ Failed to start RTMP streaming:', error);
      socket.emit('error', { message: 'Failed to start RTMP streaming: ' + error.message });
    }
  });

  // Handle streaming status request
  socket.on('get-streaming-status', () => {
    console.log('📊 Client requested streaming status');
    const isStreaming = ffmpegProcess !== null && !ffmpegProcess.killed;
    socket.emit('streaming-status', { isStreaming });
    console.log('📡 Sent streaming status:', isStreaming);
  });

  // Handle stop RTMP request
  socket.on('stop-rtmp', async () => {
    const user = authenticatedSockets.get(socket.id);
    if (!user) {
      console.warn('🔒 Unauthorized attempt to stop RTMP streaming');
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    console.log(`⏹️ User ${user.userId} (${user.email}) requested to stop RTMP streaming`);
    try {
      await stopRTMPStreaming();
      const isStreaming = ffmpegProcess !== null && !ffmpegProcess.killed;
      socket.emit('rtmp-status', {
        streaming: isStreaming,
        message: isStreaming ? 'Failed to stop RTMP stream' : 'RTMP stream stopped'
      });
      socket.emit('streaming-status', { isStreaming });
      console.log(`📡 RTMP streaming stopped by user ${user.userId}, sent status:`, isStreaming);
    } catch (error) {
      console.error('❌ Failed to stop RTMP streaming:', error);
      socket.emit('error', { message: 'Failed to stop RTMP streaming: ' + error.message });
    }
  });

  socket.on('disconnect', (reason) => {
    const user = authenticatedSockets.get(socket.id);
    console.log(`🔌 Client disconnected: ${socket.id} (User: ${user ? user.userId : 'Unknown'}) Reason:`, reason);
    console.log('📊 Total connections remaining:', io.engine.clientsCount);

    // Clean up authenticated session
    if (user) {
      authenticatedSockets.delete(socket.id);
      console.log(`🔒 Cleaned up session for user ${user.userId}`);
    }

    // Perform cleanup to stop streaming and free resources
    cleanup();

    console.log('✅ Cleanup completed for disconnected client');
  });
});

// RTMP Streaming
async function startRTMPStreaming() {
  console.log('🚀 startRTMPStreaming() called');
  try {
    if (!producer || !router) {
      console.log('❌ Missing dependencies - producer:', !!producer, 'router:', !!router);
      throw new Error('Producer or router not available');
    }
    console.log('✅ Dependencies available, proceeding with RTP transport creation...');

    const useAudio = audioProducer !== null && audioProducer !== undefined;
    const useVideo = producer !== null && producer !== undefined;

    console.log('🎵 Audio available:', useAudio, 'Video available:', useVideo);

    // Create audio RTP transport and consumer if audio producer exists
    if (useAudio) {
      audioRtpTransport = await router.createPlainTransport({
        comedia: false,
        rtcpMux: false,
        ...CONFIG.mediasoup.plainTransport,
      });

      await audioRtpTransport.connect({
        ip: CONFIG.mediasoup.recording.ip,
        port: CONFIG.mediasoup.recording.audioPort,
        rtcpPort: CONFIG.mediasoup.recording.audioPortRtcp,
      });

      console.log('✅ Audio RTP transport connected:',
        `${audioRtpTransport.tuple.localIp}:${audioRtpTransport.tuple.localPort} <--> ${audioRtpTransport.tuple.remoteIp}:${audioRtpTransport.tuple.remotePort}`);
      console.log('📊 Audio RTP transport config:', {
        comedia: false,
        rtcpMux: false,
        listenIp: CONFIG.mediasoup.plainTransport.listenIp,
        connectIp: CONFIG.mediasoup.recording.ip,
        connectPort: CONFIG.mediasoup.recording.audioPort
      });

      audioConsumer = await audioRtpTransport.consume({
        producerId: audioProducer.id,
        rtpCapabilities: router.rtpCapabilities,
        paused: false, // Start unpaused
      });

      console.log('✅ Audio consumer created:', audioConsumer.kind, audioConsumer.type);
      console.log('📊 Audio consumer RTP parameters:', {
        ssrc: audioConsumer.rtpParameters.encodings[0]?.ssrc,
        payloadType: audioConsumer.rtpParameters.codecs[0]?.payloadType,
        mimeType: audioConsumer.rtpParameters.codecs[0]?.mimeType
      });
    } else {
      console.log('⚠️ No audio producer available, proceeding with video only');
    }

    // Create video RTP transport and consumer
    if (useVideo) {
      rtpTransport = await router.createPlainTransport({
        comedia: false,
        rtcpMux: false,
        ...CONFIG.mediasoup.plainTransport,
      });

      await rtpTransport.connect({
        ip: CONFIG.mediasoup.recording.ip,
        port: CONFIG.mediasoup.recording.videoPort,
        rtcpPort: CONFIG.mediasoup.recording.videoPortRtcp,
      });

      console.log('✅ Video RTP transport connected:',
        `${rtpTransport.tuple.localIp}:${rtpTransport.tuple.localPort} <--> ${rtpTransport.tuple.remoteIp}:${rtpTransport.tuple.remotePort}`);
      console.log('📊 Video RTP transport config:', {
        comedia: false,
        rtcpMux: false,
        listenIp: CONFIG.mediasoup.plainTransport.listenIp,
        connectIp: CONFIG.mediasoup.recording.ip,
        connectPort: CONFIG.mediasoup.recording.videoPort
      });

      consumer = await rtpTransport.consume({
        producerId: producer.id,
        rtpCapabilities: router.rtpCapabilities,
        paused: false, // Start unpaused to begin data flow immediately
      });

      console.log('✅ Video consumer created:', consumer.kind, consumer.type);
      console.log('📊 Video consumer RTP parameters:', {
        ssrc: consumer.rtpParameters.encodings[0]?.ssrc,
        payloadType: consumer.rtpParameters.codecs[0]?.payloadType,
        mimeType: consumer.rtpParameters.codecs[0]?.mimeType
      });
    }

    // Start FFmpeg process BEFORE resuming consumers (like mediasoup-recording)
    await startFFmpeg();

    // Consumers are now created unpaused, so they should start flowing immediately
    console.log('📊 Audio consumer paused:', audioConsumer?.paused);
    console.log('📊 Video consumer paused:', consumer?.paused);

    if (useVideo && consumer) {
      // Start keyframe interval for video
      startKeyframeInterval();
    }

    // Start monitoring producer stats immediately
    startProducerStatsMonitoring();

    console.log('✅ RTMP streaming started with audio:', useAudio, 'video:', useVideo);

  } catch (error) {
    console.error('❌ Failed to start RTMP streaming:', error);
  }
}

// Keyframe interval management (like mediasoup-recording)
function startKeyframeInterval() {
  if (keyframeInterval) {
    clearInterval(keyframeInterval);
  }

  keyframeInterval = setInterval(() => {
    if (consumer && !consumer.closed) {
      // Request keyframe from the consumer (not producer)
      consumer.requestKeyFrame()
        .then(() => console.log('🔑 Keyframe requested'))
        .catch(err => console.log('⚠️ Keyframe request failed:', err));
    }
  }, 2000); // Every 2 seconds like mediasoup-recording

  console.log('🔑 Keyframe interval started');
}

function stopKeyframeInterval() {
  if (keyframeInterval) {
    clearInterval(keyframeInterval);
    keyframeInterval = null;
    console.log('🔑 Keyframe interval stopped');
  }
}

// FFmpeg RTMP streaming
async function startFFmpeg() {
  return new Promise((resolve, reject) => {
    // Intelligently choose FFmpeg path: system FFmpeg (ARM64) or ffmpeg-static (local dev)
    let ffmpegPath;
    try {
      // Try system FFmpeg first (preferred for ARM64 deployment)
      const { execSync } = require('child_process');
      execSync('which ffmpeg', { stdio: 'ignore' });
      ffmpegPath = 'ffmpeg';
      console.log('🎬 Using system FFmpeg (ARM64 compatible)');
    } catch (error) {
      // Fallback to ffmpeg-static for local development
      try {
        ffmpegPath = require('ffmpeg-static');
        console.log('🎬 Using ffmpeg-static (local development)');
      } catch (staticError) {
        reject(new Error('FFmpeg not available: neither system FFmpeg nor ffmpeg-static found'));
        return;
      }
    }

    // Check if RTMP URL is provided by frontend
    if (!rtmpUrl) {
      const error = new Error('RTMP URL not configured. Please set RTMP URL before starting stream.');
      console.error('❌', error.message);
      reject(error);
      return;
    }

    console.log('🎯 Using RTMP URL provided by frontend:', rtmpUrl);

    // Build FFmpeg arguments like mediasoup-recording
    const useAudio = audioProducer !== null;
    const useVideo = producer !== null;

    let args = [
      '-nostdin',
      '-protocol_whitelist', 'file,rtp,udp',
      '-fflags', '+genpts',
      '-v', 'verbose',  // Add verbose logging
      '-i', `${__dirname}/recording/input-h264.sdp`
    ];

    // Add codec mappings
    if (useAudio) {
      args.push('-map', '0:a:0', '-c:a', 'aac');
    }
    if (useVideo) {
      args.push('-map', '0:v:0', '-c:v', 'copy');
    }

    // Add output format and destination
    args.push('-f', 'flv', '-rtmp_live', 'live', rtmpUrl);

    console.log('🎬 Starting FFmpeg with audio:', useAudio, 'video:', useVideo);
    console.log('🎬 FFmpeg args:', args.join(' '));

    ffmpegProcess = spawn(ffmpegPath, args);

    ffmpegProcess.on('error', (err) => {
      console.error('❌ FFmpeg error:', err);
      reject(err);
    });

    // Add timeout to detect if FFmpeg is stuck
    const ffmpegTimeout = setTimeout(() => {
      console.log('⚠️ FFmpeg timeout - no stream mapping detected after 10 seconds');
      console.log('⚠️ This might indicate RTP packets are not reaching FFmpeg');
      resolve(); // Don't reject, just continue
    }, 10000);

    ffmpegProcess.on('exit', (code, signal) => {
      console.log('🎬 FFmpeg exited with code:', code, 'signal:', signal);
      ffmpegProcess = null;
    });

    ffmpegProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.log('FFmpeg:', output);

      // Log specific events
      if (output.includes('Input #0')) {
        console.log('📊 FFmpeg detected input stream');
      }
      if (output.includes('Stream mapping:')) {
        console.log('📊 FFmpeg stream mapping established');
      }
      if (output.includes('frame=')) {
        console.log('📊 FFmpeg processing frames');
      }
      if (output.includes('Connection to tcp://')) {
        console.log('📊 FFmpeg connecting to RTMP server');
      }
      if (output.includes('Stream publish started')) {
        console.log('📊 FFmpeg RTMP publish started');
      }

      // Resolve when FFmpeg starts processing
      if (output.includes('Stream mapping:') || output.includes('frame=')) {
        clearTimeout(ffmpegTimeout);
        resolve();
      }
    });

    ffmpegProcess.stdout.on('data', (data) => {
      console.log('FFmpeg stdout:', data.toString());
    });

    // Create SDP file for FFmpeg input
    createSDPFile();

    // Test network connectivity for RTP ports
    testRTPPortConnectivity();

    // Test if RTP ports are actually receiving data
    testRTPDataFlow();
  });
}

function createSDPFile() {
  const useAudio = audioProducer !== null && audioProducer !== undefined;
  const useVideo = producer !== null && producer !== undefined;

  // Create SDP file based on available producers
  let sdpContent = `v=0
o=- 0 0 IN IP4 127.0.0.1
s=-
c=IN IP4 127.0.0.1
t=0 0
`;

  if (useAudio) {
    sdpContent += `m=audio ${CONFIG.mediasoup.recording.audioPort} RTP/AVPF 111
a=rtcp:${CONFIG.mediasoup.recording.audioPortRtcp}
a=rtpmap:111 opus/48000/2
a=fmtp:111 minptime=10;useinbandfec=1
`;
  }

  if (useVideo) {
    sdpContent += `m=video ${CONFIG.mediasoup.recording.videoPort} RTP/AVPF 125
a=rtcp:${CONFIG.mediasoup.recording.videoPortRtcp}
a=rtpmap:125 H264/90000
a=fmtp:125 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f
a=framerate:30
b=AS:3000
`;
  }

  const sdpPath = `${__dirname}/recording/input-h264.sdp`;
  require('fs').writeFileSync(sdpPath, sdpContent);
  console.log('✅ SDP file created with audio:', useAudio, 'video:', useVideo);
  console.log('📊 SDP file path:', sdpPath);
  if (useAudio) {
    console.log('📊 Audio port:', CONFIG.mediasoup.recording.audioPort, 'RTCP:', CONFIG.mediasoup.recording.audioPortRtcp);
  }
  if (useVideo) {
    console.log('📊 Video port:', CONFIG.mediasoup.recording.videoPort, 'RTCP:', CONFIG.mediasoup.recording.videoPortRtcp);
  }
}

function testRTPPortConnectivity() {
  const dgram = require('dgram');

  console.log('🔍 Testing RTP port connectivity...');

  // Test UDP port binding for RTP
  const testPorts = [
    CONFIG.mediasoup.recording.audioPort,
    CONFIG.mediasoup.recording.videoPort
  ];

  testPorts.forEach(port => {
    const socket = dgram.createSocket('udp4');

    socket.bind(port, '127.0.0.1', () => {
      console.log(`✅ UDP port ${port} is available on 127.0.0.1`);
      socket.close();
    });

    socket.on('error', (err) => {
      console.log(`❌ UDP port ${port} test failed:`, err.message);
      socket.close();
    });
  });

  // Check for any processes using RTP ports
  try {
    const { execSync } = require('child_process');
    const netstatOutput = execSync('netstat -tulpn | grep ":500[4-7]"', { encoding: 'utf8', stdio: 'pipe' });
    if (netstatOutput.trim()) {
      console.log('📊 Processes using RTP ports:', netstatOutput.trim());
    } else {
      console.log('📊 No processes currently using RTP ports 5004-5007');
    }
  } catch (error) {
    console.log('📊 Could not check port usage:', error.message);
  }
}

function testRTPDataFlow() {
  const dgram = require('dgram');

  console.log('🔍 Testing RTP data flow...');

  // Create UDP sockets to listen on RTP ports and see if we receive any data
  const testPorts = [
    CONFIG.mediasoup.recording.audioPort,
    CONFIG.mediasoup.recording.videoPort
  ];

  testPorts.forEach(port => {
    const socket = dgram.createSocket('udp4');
    let packetCount = 0;

    socket.bind(port + 1000, '127.0.0.1', () => { // Use offset ports to avoid conflicts
      console.log(`🔍 Test socket listening on 127.0.0.1:${port + 1000} (monitoring ${port})`);
    });

    socket.on('message', (msg, rinfo) => {
      packetCount++;
      if (packetCount <= 5) { // Only log first few packets
        console.log(`📦 Test socket received packet on port ${port + 1000}: ${msg.length} bytes from ${rinfo.address}:${rinfo.port}`);
      }
    });

    socket.on('error', (err) => {
      console.log(`❌ Test socket error on port ${port + 1000}:`, err.message);
    });

    // Close test socket after 30 seconds
    setTimeout(() => {
      console.log(`📊 Test socket on port ${port + 1000} received ${packetCount} packets total`);
      socket.close();
    }, 30000);
  });
}

// Stop RTMP streaming
async function stopRTMPStreaming() {
  console.log('⏹️ stopRTMPStreaming() called');
  try {
    if (ffmpegProcess) {
      console.log('⏹️ Stopping FFmpeg process');
      ffmpegProcess.kill('SIGINT');
      ffmpegProcess = null;
    }

    if (keyframeInterval) {
      console.log('⏹️ Stopping keyframe interval');
      clearInterval(keyframeInterval);
      keyframeInterval = null;
    }

    if (consumer) {
      console.log('⏹️ Closing video consumer');
      consumer.close();
      consumer = null;
    }

    if (audioConsumer) {
      console.log('⏹️ Closing audio consumer');
      audioConsumer.close();
      audioConsumer = null;
    }

    if (rtpTransport) {
      console.log('⏹️ Closing RTP transport');
      rtpTransport.close();
      rtpTransport = null;
    }

    console.log('✅ RTMP streaming stopped');
  } catch (error) {
    console.error('❌ Failed to stop RTMP streaming:', error);
    throw error;
  }
}

// Cleanup function
function cleanup() {
  console.log('🧹 Starting cleanup process...');

  // Stop FFmpeg process
  if (ffmpegProcess) {
    console.log('⏹️ Stopping FFmpeg process');
    ffmpegProcess.kill('SIGINT');
    ffmpegProcess = null;
  }

  // Clear all intervals
  if (keyframeInterval) {
    console.log('⏹️ Clearing keyframe interval');
    clearInterval(keyframeInterval);
    keyframeInterval = null;
  }

  if (statsInterval) {
    console.log('⏹️ Clearing video stats interval');
    clearInterval(statsInterval);
    statsInterval = null;
  }

  if (audioStatsInterval) {
    console.log('⏹️ Clearing audio stats interval');
    clearInterval(audioStatsInterval);
    audioStatsInterval = null;
  }

  // Close consumers
  if (consumer) {
    console.log('⏹️ Closing video consumer');
    consumer.close();
    consumer = null;
  }

  if (audioConsumer) {
    console.log('⏹️ Closing audio consumer');
    audioConsumer.close();
    audioConsumer = null;
  }

  // Close producers
  if (audioProducer) {
    console.log('⏹️ Closing audio producer');
    audioProducer.close();
    audioProducer = null;
  }

  if (producer) {
    console.log('⏹️ Closing video producer');
    producer.close();
    producer = null;
  }

  // Close transports
  if (rtpTransport) {
    console.log('⏹️ Closing RTP transport');
    rtpTransport.close();
    rtpTransport = null;
  }

  if (audioRtpTransport) {
    console.log('⏹️ Closing audio RTP transport');
    audioRtpTransport.close();
    audioRtpTransport = null;
  }

  if (webRtcTransport) {
    console.log('⏹️ Closing WebRTC transport');
    webRtcTransport.close();
    webRtcTransport = null;
  }

  console.log('✅ Cleanup completed');
}

// Create recording directory
const recordingDir = `${__dirname}/recording`;
if (!fs.existsSync(recordingDir)) {
  fs.mkdirSync(recordingDir, { recursive: true });
}

// Start server
async function startServer() {
  try {
    const initialized = await initializeMediasoup();
    if (!initialized) {
      process.exit(1);
    }

    const PORT = process.env.MEDIA_SERVER_PORT || process.env.PORT || 8081;
    const HOST = process.env.LISTEN_IP || '0.0.0.0';

    server.listen(PORT, HOST, () => {
      console.log(`🚀 Media Server running on ${HOST}:${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/`);
      console.log(`🌐 External access: https://${process.env.ANNOUNCED_IP || HOST}:${PORT}/`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  cleanup();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

