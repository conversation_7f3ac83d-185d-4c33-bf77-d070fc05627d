# Media Server

A WebRTC-to-RTMP conversion service using mediasoup for real-time video streaming.

## Features

- **WebRTC video stream ingestion** with mediasoup 3.19.2
- **Real-time conversion to RTMP** using native FFmpeg 4.4.2
- **ARM64 optimized** for Oracle Cloud Infrastructure
- **Ubuntu 22.04 LTS** for maximum compatibility and performance
- **Prebuilt binaries** for faster deployment (no compilation required)
- **Configurable video encoding parameters**
- **Low memory footprint** (~28MB typical usage)

## Quick Start

### Local Development

1. Install dependencies:
```bash
npm install
```

2. Copy environment configuration:
```bash
cp .env.example .env
```

3. Start the service:
```bash
npm start
```

The service will be available at `http://localhost:8081`

### Production Deployment (OCI ARM64)

The media server is optimized for deployment on Oracle Cloud Infrastructure ARM64 instances:

```bash
cd terraform/environments/oci-dev
terraform apply
```

This deploys:
- **Ubuntu 22.04 LTS** ARM64 instance
- **mediasoup 3.19.2** with prebuilt ARM64 binaries
- **Native FFmpeg 4.4.2** with full codec support
- **Automatic service management** with systemd
- **Firewall configuration** for WebRTC and HTTP traffic

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `8081` |
| `LISTEN_IP` | IP address to bind to | `0.0.0.0` |
| `ANNOUNCED_IP` | Public IP for WebRTC | Auto-detected |
| `MEDIASOUP_LOG_LEVEL` | Logging level | `warn` |
| `RTC_MIN_PORT` | Minimum RTC port | `32256` |
| `RTC_MAX_PORT` | Maximum RTC port | `65535` |
| `MAX_BITRATE` | Maximum video bitrate | `3000000` |
| `MAX_FRAMERATE` | Maximum framerate | `30.0` |

### Network Requirements

- **Ports**: The service requires access to the configured RTC port range (default: 32256-65535)
- **Protocols**: UDP and TCP for WebRTC communication
- **Firewall**: Ensure the port range is open for incoming connections

## API Usage

### WebSocket Events

The service communicates via WebSocket using Socket.io:

#### Client → Server Events

- `get-transport-info`: Request WebRTC transport information
- `connect-transport`: Connect WebRTC transport with DTLS parameters
- `create-producer`: Create a media producer
- `start-rtmp`: Start RTMP streaming
- `stop-rtmp`: Stop RTMP streaming

#### Server → Client Events

- `transport-info`: WebRTC transport information
- `transport-connected`: Transport connection confirmation
- `producer-created`: Producer creation confirmation
- `rtmp-status`: RTMP streaming status updates
- `error`: Error messages

### Example Client Integration

```javascript
import { io } from 'socket.io-client';
import { Device } from 'mediasoup-client';

const socket = io('wss://your-service-url');
const device = new Device();

// Get transport info and create producer
socket.emit('get-transport-info', { roomId: 'room1' });
socket.on('transport-info', async (transportInfo) => {
  // Create and connect transport
  // Create producer with video stream
  // Start RTMP streaming
});
```

## Monitoring

### Health Check

The service provides a health check endpoint:

```bash
curl http://localhost:8080/
```

### Logs

View logs in GCP Cloud Run:

```bash
gcloud logs tail --service=media-server
```

## Troubleshooting

### Common Issues

1. **WebRTC Connection Failed**
   - Check firewall settings for RTC port range
   - Verify `ANNOUNCED_IP` is set correctly for public deployments

2. **FFmpeg Errors**
   - Ensure FFmpeg is properly installed in the container
   - Check video codec compatibility

3. **High CPU Usage**
   - Adjust video encoding parameters
   - Consider scaling horizontally

### Debug Mode

Enable debug logging:

```bash
export MEDIASOUP_LOG_LEVEL=debug
npm start
```

## Architecture

```
Client (WebGL) → WebRTC → Mediasoup → RTP → FFmpeg → RTMP → Streaming Platform
```

1. **Client**: Captures WebGL canvas as MediaStream
2. **WebRTC**: Transmits video to mediasoup service
3. **Mediasoup**: Handles WebRTC signaling and media routing
4. **RTP**: Converts WebRTC to RTP for FFmpeg
5. **FFmpeg**: Transcodes RTP to RTMP
6. **RTMP**: Streams to platforms like Twitch, YouTube, etc.

## License

MIT License
